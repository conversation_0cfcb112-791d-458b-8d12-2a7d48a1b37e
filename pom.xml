<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.cottoneasy</groupId>
	<artifactId>cnce-portal-server</artifactId>
	<packaging>war</packaging>
	<version>3.0.0</version>
	<parent>
		<groupId>org.harry.dandelionframework</groupId>
		<artifactId>dandelion-framework-boot</artifactId>
		<version>2.3.0</version>
	</parent>

	<!-- 发布服务器配置 -->
	<distributionManagement>
		<!-- 生产环境 -->
		<repository>
			<id>dandelion-releases</id>
			<name>Releases</name>
			<url>http://101.35.227.2:8081/repository/maven-releases/</url>
		</repository>

		<!-- 灾备环境 -->
		<!--<repository>
			<id>cnce-zb-releases</id>
			<name>Releases</name>
			<url>http://118.1.10.15:8081/repository/maven-releases/</url>
		</repository>-->

		<!-- 开发环境-jenkins部署使用 -->
		<!--<repository>
			<id>dandelion-releases</id>
			<name>Releases</name>
			<url>http://118.4.40.25:18081/repository/maven-releases/</url>
		</repository>-->

		<!-- 开发环境-本地打包使用 -->
		<!--<repository>
			<id>cnce-sit-releases</id>
			<name>Releases</name>
			<url>http://118.4.40.25:18081/repository/maven-releases/</url>
		</repository>-->
	</distributionManagement>

	<dependencies>

		<dependency>
			<groupId>org.harry</groupId>
			<artifactId>dandelion-portal</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>org.harry.dandelionframework</groupId>
			<artifactId>dandelion-signature-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.cfca</groupId>
					<artifactId>PaperlessClient</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.cfca</groupId>
			<artifactId>PaperlessClient</artifactId>
			<version>4.0.0.5</version>
		</dependency>

		<dependency>
			<groupId>org.harry.dandelionframework</groupId>
			<artifactId>dandelion-framework-document-actions</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<version>2.6.14</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>5.3.24</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.harry.dandelionframework</groupId>
			<artifactId>dandelion-fms-sdk</artifactId>
		</dependency>

		<dependency>
			<groupId>com.cottoneasy</groupId>
			<artifactId>cnce-portal-server-sdk</artifactId>
			<version>3.0.1</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<classifier>${profile.name}</classifier>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<compilerArguments>
						<bootclasspath>${java.home}/lib/rt.jar</bootclasspath>
					</compilerArguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>pdf</nonFilteredFileExtension>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
						<nonFilteredFileExtension>TTF</nonFilteredFileExtension>
						<nonFilteredFileExtension>html</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
