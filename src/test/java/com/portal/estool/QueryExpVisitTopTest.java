package com.portal.estool;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.portal.TestPortalApplication;
import com.sinosoft.dandelion.system.client.model.log.SuperDandelionLog;
import com.sinosoft.dandelion.system.client.model.log.SystemAccessLogEsInfo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.FiltersAggregator;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Stats;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.harry.dandelion.framework.common.utils.FileUtils;
import org.harry.dandelion.framework.core.log.SystemAccessLogInfo;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.zxp.esclientrhl.enums.AggsType;
import org.zxp.esclientrhl.enums.SqlFormat;
import org.zxp.esclientrhl.repository.*;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestPortalApplication.class)
@Slf4j
public class QueryExpVisitTopTest {

    @Autowired(required = false)
    private ElasticsearchTemplate<SystemAccessLogEsInfo, String> elasticsearchTemplate;

    String basePath = "D:\\06_data\\es\\prod";

    /**
     * 查询并导出交易商的业务访问情况，不限制时间，且排除通用的服务
     */
    @Test
    public void queryEs1() {
        try {
            //String traderRes = elasticsearchTemplate.queryBySQL("select customName from super_dandelion_access_logs where customType = '1' and logType='LoginLog' group by customName", SqlFormat.JSON);
            // 查询交易商列表
            //List<String> customList = this.asyncCustom(traderRes);
            List<String> customList = this.asyncCustomFromCsv();
            if(!(customList != null && customList.size() > 0)){
                log.debug("无数据！");
                return;
            }
            int i = 0;
            for(String custom : customList){
                String result = elasticsearchTemplate.queryBySQL("select customName,requestServiceGroups,requestServiceName,requestURI,count(requestURI) from super_dandelion_access_logs where customType = '1' and logType='OpLog' and requestServiceGroups != '' and requestServiceGroups is not null and requestServiceName != '' and requestServiceName NOT LIKE '%校验%' and requestServiceGroups NOT LIKE '%公共%' and requestServiceGroups NOT LIKE '%SYSHOME%' and requestServiceGroups NOT LIKE '%文件服务%' and requestServiceGroups NOT LIKE '%cms%' and requestURI NOT LIKE '/portal%' and customName='"+custom+"' group by customName,requestURI,requestServiceGroups,requestServiceName order by count(requestURI) desc", SqlFormat.JSON);
                this.asyncData(result, custom);
                i++;
            }
            log.debug("共处理{}条数据",i);
        } catch (Exception e) {
            log.error("操作日志查询错误",e);
            e.printStackTrace();
        }
    }

    private List<String> asyncCustomFromCsv() throws IOException {
        String csvPath = "D:\\06_data\\es\\traders.csv";
        String DELIMITER = ",";
        List<String> traderNameList = new ArrayList<>();
        try (BufferedReader br = Files.newBufferedReader(Paths.get(csvPath))) {
            String line;
            while ((line = br.readLine()) != null) {
                if(StringUtil.isBlank(line)){
                    break;
                }
                if(line.trim().equals("customName")){
                    continue;
                }
                if(StringUtil.containsAny(line, "测试")){
                    continue;
                }
                //String[] columns = line.split(DELIMITER);
                traderNameList.add(line.trim());
            }
        } catch (IOException ex) {
            log.error("读取csv文件出错",ex);
        }
        return traderNameList;
    }

    private List<String> asyncCustom(String result) throws IOException {
        JSONObject jsonObject = JSONUtil.parseObj(result);
        JSONArray jsonArray = jsonObject.get("rows", JSONArray.class);

        List<String> traderNameList = new ArrayList<>();
        VisitVo visitVo;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray jsonArray1 = jsonArray.get(i, JSONArray.class);
            String traderName = jsonArray1.get(0, String.class);
            traderNameList.add(traderName);
        }
        return traderNameList;
    }

    private void asyncData(String result, String customName) throws IOException {
        String fileName = customName + ".json";
        String filePath = basePath + File.separator + fileName;
        FileUtils.createFile(result.getBytes(), basePath, fileName);
        JSONObject jsonObject = JSONUtil.readJSONObject(new File(filePath), Charset.defaultCharset());
        JSONArray jsonArray = jsonObject.get("rows", JSONArray.class);

        List<VisitVo> visitList = new ArrayList<>();
        VisitVo visitVo;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray _array = jsonArray.get(i, JSONArray.class);
            visitVo = new VisitVo();
            visitVo.setTraderName(_array.get(0, String.class));
            visitVo.setRequestServiceGroups(_array.get(1, String.class));
            visitVo.setRequestServiceName(_array.get(2, String.class));
            visitVo.setRequestURI(_array.get(3, String.class));
            visitVo.setCount(_array.get(4, Integer.class));
            visitList.add(visitVo);
        }
        this.exportExcel(visitList, customName);
        FileUtils.delete(filePath);
    }

    // 导出到Excel
    private void exportExcel(List<VisitVo> allCdList , String customName) {
        String filePath = basePath+File.separator+customName+".xlsx";
        File destFile = new File(filePath);
        if(!destFile.exists()) {
            try {
                destFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 通过hutool工具创建的excel的writer，默认为xls格式
        ExcelWriter writer = ExcelUtil.getWriter();
        // 设置要导出到的sheet
        writer.setSheet("sheet1");
        // 自定义excel标题和列名
        writer.addHeaderAlias("traderName", "交易商");
        writer.addHeaderAlias("requestServiceGroups", "请求服务分组名称");
        writer.addHeaderAlias("requestServiceName", "请求服务名称");
        writer.addHeaderAlias("requestURI", "请求路径");
        writer.addHeaderAlias("count", "访问次数");
        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(allCdList, true);
        writer.flush(destFile);
        writer.close();
    }
}
