package com.portal.estool;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17
 * @desc
 */public class TestMain {
    // 导出到Excel
    private static void exportExcel(List<VisitVo> allCdList , String fileName) {
        String filePath = "D:\\06_data\\es\\"+fileName;
        File destFile = new File(filePath);
        if(!destFile.exists()) {
            try {
                destFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 通过hutool工具创建的excel的writer，默认为xls格式
        ExcelWriter writer = ExcelUtil.getWriter();
        // 设置要导出到的sheet
        writer.setSheet("Sheet1");
        // 自定义excel标题和列名
        writer.addHeaderAlias("customName", "交易商");
        writer.addHeaderAlias("requestServiceGroups", "请求服务分组名称");
        writer.addHeaderAlias("requestServiceName", "请求服务名称");
        writer.addHeaderAlias("requestURI", "请求路径");
        writer.addHeaderAlias("count", "访问次数");
        //writer.addHeaderAlias("chargeStatus", "状态");
        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(allCdList, true);
        writer.flush(destFile);
        writer.close();
    }

    public static void main(String[] args) throws IOException {
        String filePath = "D:\\06_data\\es\\test.json";
        byte[] allbytes = Files.readAllBytes(Paths.get(filePath)); // 读取CSV文件的所有行
        String content = new String(allbytes, "UTF-8");
        JSONObject jsonObject = JSONUtil.readJSONObject(new File(filePath), Charset.defaultCharset());
        JSONArray jsonArray = jsonObject.get("rows", JSONArray.class);

        List<VisitVo> visitList = new ArrayList<>();
        VisitVo visitVo;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray _array = jsonArray.get(i, JSONArray.class);
            visitVo = new VisitVo();
            visitVo.setTraderName(_array.get(0, String.class));
            visitVo.setRequestServiceGroups(_array.get(1, String.class));
            visitVo.setRequestServiceName(_array.get(2, String.class));
            visitVo.setRequestURI(_array.get(3, String.class));
            visitVo.setCount(_array.get(4, Integer.class));
            visitList.add(visitVo);
        }

        //String[] headers = lines.get(0).split(","); // 第一行为header

        //for (int i = 1; i < lines.size(); i++) {
        //    String[] values = lines.get(i).split(",");

        //}
        //exportExcel(visitList, "test.xlsx");
    }
}
