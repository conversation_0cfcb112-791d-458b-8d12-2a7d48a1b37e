package com.cottoneasy.portal.complain.service;

import cn.hutool.core.date.DateUtil;
import com.cottoneasy.portal.base.entity.CmsComplainEntity;
import com.cottoneasy.portal.base.mappers.CmsComplainMapper;
import com.cottoneasy.portal.complain.vo.ComplainParamVo;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

@Service("complain.addComplain.1")
@ApiRequestObject(value = "新增投诉信息", name = "addComplain", groups = "cms-投诉服务",
        params = {@ApiParamMeta(key = "complainParamVo", desc = "投诉信息", type = ComplainParamVo.class),

        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class addComplain implements IBusinessService {
    @Resource
    private CmsComplainMapper cmsComplainMapper;


    public void doVerify(ServiceHandlerContext context) {
        ComplainParamVo complainParamVo = context.getValueObject(ComplainParamVo.class, "complainParamVo");
        if (isNotEmpty(complainParamVo)) {
            if (!isNotEmpty(complainParamVo.getAnonymous())) {
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉匿名情况不能为空");
            }
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        CmsComplainEntity entity = new CmsComplainEntity();
        ComplainParamVo complainParamVo = context.getValueObject(ComplainParamVo.class, "complainParamVo");
        BeanUtils.copyProperties(complainParamVo, entity);
        entity.setStatus("COMPLAIN_UNDEALT");//设置初始数据
        entity.setSubmitTime(DateUtil.date());
        if (complainParamVo.getAnonymous().equals("UNANONYMOUS")) {
            entity.setCustomId(complainParamVo.getCustomId());
            entity.setTelphone(complainParamVo.getTelphone());
//            entity.setCustomName(context.getCurrentUserRealName());
        } else {
            entity.setCustomId(null);
            entity.setCustomName(null);
        }
        cmsComplainMapper.insert(entity);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", true);
    }
}
