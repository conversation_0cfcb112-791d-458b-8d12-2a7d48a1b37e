package com.cottoneasy.portal.complain.service;

import cn.hutool.core.date.DateUtil;
import com.cottoneasy.portal.base.entity.CmsComplainEntity;
import com.cottoneasy.portal.base.mappers.CmsComplainMapper;
import com.cottoneasy.portal.complain.vo.ComplainParamVo;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;
import static cn.hutool.core.util.ObjectUtil.isNull;

@Service("complain.editComplain.1")
@ApiRequestObject(value = "处理投诉信息", name = "editComplain", groups = "cms-投诉服务", params = {
        @ApiParamMeta(key = "complainParamVo", desc = "处理投诉信息", type = ComplainParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class editComplain implements IBusinessService {
    @Resource
    private CmsComplainMapper cmsComplainMapper;


    public void doVerify(ServiceHandlerContext context) {
        ComplainParamVo complainParamVo = context.getValueObject(ComplainParamVo.class, "complainParamVo");
        if (isNull(complainParamVo)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉对象不能为空！");
        }
        if (!isNotEmpty(complainParamVo.getComplainId())) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉主键不能为空！");
        }
        if (!isNotEmpty(complainParamVo.getDealResult())) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉结果不能为空！");
        }
        if (!isNotEmpty(complainParamVo.getStatus())) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "处理状态不能为空！");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        CmsComplainEntity entity = new CmsComplainEntity();
        ComplainParamVo complainParamVo = context.getValueObject(ComplainParamVo.class, "complainParamVo");
        entity.setComplainId(complainParamVo.getComplainId());
        entity.setStatus("COMPLAIN_DEAL");
        entity.setDealResult(complainParamVo.getDealResult());
        entity.setDealTime(DateUtil.date());
        entity.setDealUserId(context.getCurrentUserId());
        entity.setDealUserName(context.getCurrentUserRealName());
        cmsComplainMapper.updateById(entity);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("message", "处理完成");
    }
}
