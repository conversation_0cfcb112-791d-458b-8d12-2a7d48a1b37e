package com.cottoneasy.portal.complain.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.CmsComplainEntity;
import com.cottoneasy.portal.base.mappers.CmsComplainMapper;
import com.cottoneasy.portal.complain.vo.ComplainParamVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

@Service("complain.complainPage.1")
@ApiRequestObject(value = "分页查询投诉信息", name = "complainPage", groups = "cms-投诉服务",
        params = {@ApiParamMeta(key = "searchVo", desc = "投诉信息分页", type = ComplainParamVo.class),

        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class complainPage implements IBusinessService {
    @Resource
    private CmsComplainMapper cmsComplainMapper;


    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        ComplainParamVo complainParamVo = context.getValueObject(ComplainParamVo.class, "searchVo");
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        PipPagination<ComplainParamVo> page = new PipPagination<>(pageParameter);
        LambdaQueryWrapper<CmsComplainEntity> queryWrapper = Wrappers.lambdaQuery(CmsComplainEntity.class);
        this.setParams(queryWrapper, complainParamVo);
        page = cmsComplainMapper.selectComplainPage(page, queryWrapper);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", page);
    }


    @SuppressWarnings("unchecked")
	private void setParams(LambdaQueryWrapper<CmsComplainEntity> queryWrapper, ComplainParamVo complainParamVo) {

        if (isNotEmpty(complainParamVo)) {

            if (isNotEmpty(complainParamVo.getComplainId())) {
                queryWrapper.in(CmsComplainEntity::getComplainId, complainParamVo.getComplainId());
            }
            if (isNotEmpty(complainParamVo.getCustomId())) {
                queryWrapper.like(CmsComplainEntity::getCustomId, complainParamVo.getCustomId());
            }
            if (isNotEmpty(complainParamVo.getCustomName())) {
                queryWrapper.like(CmsComplainEntity::getCustomName, complainParamVo.getCustomName());
            }
            if (isNotEmpty(complainParamVo.getContent())) {
                queryWrapper.like(CmsComplainEntity::getContent, complainParamVo.getContent());
            }
            if (isNotEmpty(complainParamVo.getSubmitTime())) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(complainParamVo.getSubmitTime());
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                queryWrapper.between(CmsComplainEntity::getSubmitTime, complainParamVo.getSubmitTime(), calendar.getTime());
            }
            if (isNotEmpty(complainParamVo.getStatus())) {
                queryWrapper.in(CmsComplainEntity::getStatus, complainParamVo.getStatus());
            }
            if (isNotEmpty(complainParamVo.getAnonymous())) {
                queryWrapper.in(CmsComplainEntity::getAnonymous, complainParamVo.getAnonymous());
            }
            queryWrapper.orderByDesc(Boolean.parseBoolean("submit_time"));
        }
    }
}
