package com.cottoneasy.portal.complain.service;

import com.cottoneasy.portal.base.entity.CmsComplainEntity;
import com.cottoneasy.portal.base.mappers.CmsComplainMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;

@Service("complain.detailComplain.1")
@ApiRequestObject(value = "投诉信息详情", name = "detailComplain", groups = "cms-投诉服务",
        params = {@ApiParamMeta(key = "id", desc = "投诉信息详情", type = String.class),
        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class detailComplain implements IBusinessService {
    @Resource
    private CmsComplainMapper cmsComplainMapper;
    String id = null;

    public void doVerify(ServiceHandlerContext context) {
        id = context.getValueObject(String.class, "id");
        if (!isNotEmpty(id)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉主键不能为空！");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        CmsComplainEntity entity = cmsComplainMapper.selectById(id);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", entity);
    }
}
