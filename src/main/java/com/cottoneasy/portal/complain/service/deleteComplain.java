package com.cottoneasy.portal.complain.service;

import com.cottoneasy.portal.base.mappers.CmsComplainMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("complain.deleteComplain.1")
@ApiRequestObject(value = "删除投诉信息", name = "deleteComplain", groups = "cms-投诉服务",
        params = {@ApiParamMeta(key = "ids", desc = "投诉信息删除", type = String.class),
        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class deleteComplain implements IBusinessService {
    @Resource
    private CmsComplainMapper cmsComplainMapper;


    public void doVerify(ServiceHandlerContext context) {
        String ids = context.getStringValue("ids");
        if (StringUtil.isEmpty(ids)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "投诉id不能为空！");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ids = context.getStringValue("ids");
        String[] split = ids.split(",");
        for (String id : split) {
            cmsComplainMapper.deleteById(id);
        }
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", true);
    }
}
