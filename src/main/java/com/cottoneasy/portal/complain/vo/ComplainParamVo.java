package com.cottoneasy.portal.complain.vo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
public class ComplainParamVo implements Serializable {

    private static final long serialVersionUID = 3617380919610531580L;
    /**
     * yhl
     * 用于增删改查的方法使用
     */


    private String complainId;

    /**
     * 客户名称
     */
    private String customName;


    private String anonymous;

    /**
     * 客户id
     */
    private String customId;

    /**
     * 投诉内容
     */
    private String content;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 处理状态（待处理，已处理）
     */
    private String status;

    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 处理时间
     */
    private Date dealTime;

    /**
     * 处理人id
     */
    private String dealUserId;

    /**
     * 处理人名字
     */
    private String dealUserName;
    @FieldEncrypt
    private String telphone;


}
