package com.cottoneasy.portal.agreement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.UserLoginAgreement;
import com.cottoneasy.portal.base.mappers.UserLoginAgreementMapper;
import org.harry.dandelion.framework.core.message.response.annonation.ResponseDataSet;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: GetLoginAgreement
 * @Description: 获取登录协议
 * <AUTHOR>
 * @date 2023年2月6日 上午11:53:35
 *
 */
@Service("loginagreement.getloginagreement.1")
@ApiRequestObject(value = "获取登录协议", name = "GetLoginAgreement", groups = {"登录协议"})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "displayName", desc = "登录页面，check展示名称", type = String.class,example = "电子仓单文件声明"),
        @ApiParamMeta(key = "title", desc = "声明文件标题", type = String.class),
        @ApiParamMeta(key = "content", desc = "声明文件内容", type = String.class)
        
})
@ResponseDataSet
public class GetLoginAgreement implements IBusinessService {

	@Resource
	private UserLoginAgreementMapper loginAgreement;

	@Override
	public void doVerify(ServiceHandlerContext context) {
		 
	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		LambdaQueryWrapper<UserLoginAgreement> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(UserLoginAgreement::getDefaultAgreement,"0");
		List<UserLoginAgreement> agreementList = loginAgreement.selectList(queryWrapper);
		if (agreementList != null && agreementList.size() > 0){
			UserLoginAgreement agreement = agreementList.get(0);
			this.createSuccessResponse(context);
			context.getResponseBody().getDataSet().put("displayName", agreement.getDisplayName());
			context.getResponseBody().getDataSet().put("title", agreement.getTitle());
			context.getResponseBody().getDataSet().put("content", agreement.getContent());
		}else {
			this.createSuccessResponse(context);
			context.getResponseBody().getDataSet().put("displayName", "");
			context.getResponseBody().getDataSet().put("title", "");
			context.getResponseBody().getDataSet().put("content", "");
		}
	}

}
