package com.cottoneasy.portal.agreement;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.UserLoginAgreeRecord;
import com.cottoneasy.portal.base.entity.UserLoginAgreement;
import com.cottoneasy.portal.base.mappers.UserLoginAgreeRecordMapper;
import com.cottoneasy.portal.base.mappers.UserLoginAgreementMapper;
import com.sinosoft.dandelion.system.client.EngineService;
import com.sinosoft.dandelion.system.client.model.Custom;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.message.response.annonation.ResponseDataSet;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: RequireCheckedAgreement
 * @Description: 验证用户是否需要同意协议后登录
 * <AUTHOR>
 * @date 2023年2月6日 下午2:11:04
 * 0:不需要  1:需要  2:默认选中同意
 */
@Service("loginagreement.requirecheckedagreement.1")
@ApiRequestObject(value = "是否需要同意登录协议", name = "RequireCheckedAgreement", groups = {"登录协议"}, params = {
        @ApiParamMeta(key = "customCode", desc = "企业代码", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "checked", desc = "状态", type = String.class,example = "0:不需要  1:需要  2:默认选中同意(checkbox选中)")
})
@ResponseDataSet
public class RequireCheckedAgreement implements IBusinessService {

	@Resource
	private UserLoginAgreeRecordMapper loginAgreeRecord;

	@Resource
	private UserLoginAgreementMapper loginAgreement;

	@Override
	public void doVerify(ServiceHandlerContext context) {
		String customCode = context.getValueObject(String.class, "customCode");
		if (StringUtil.isEmpty(customCode)){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请输入业务代码！");
		}

	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		String customCode = context.getValueObject(String.class, "customCode");
		Custom custom = EngineService.getCustomService().getCustomByCode(customCode);
		if (custom != null){
			//查询协议id
			LambdaQueryWrapper<UserLoginAgreement> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.eq(UserLoginAgreement::getDefaultAgreement,"0");
			UserLoginAgreement agreement = loginAgreement.selectOne(queryWrapper);
			//根据协议id和企业id查询记录表中是否有记录
			LambdaQueryWrapper<UserLoginAgreeRecord> query = Wrappers.lambdaQuery();
			query.eq(UserLoginAgreeRecord::getEnterpriseId,custom.getEnterpriseId());
			query.eq(UserLoginAgreeRecord::getAgreementId,agreement.getId());
			UserLoginAgreeRecord agreeRecord = loginAgreeRecord.selectOne(query);
			if (agreeRecord != null){
				this.createSuccessResponse(context);
				context.getResponseBody().getDataSet().put("checked", "2");
			}else {
				this.createSuccessResponse(context);
				context.getResponseBody().getDataSet().put("checked", "1");
			}
		}else {
			this.createSuccessResponse(context);
			context.getResponseBody().getDataSet().put("checked", null);
		}

	}

}
