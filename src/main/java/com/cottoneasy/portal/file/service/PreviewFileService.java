package com.cottoneasy.portal.file.service;

import com.sinosoft.dandelion.fms.base.FileInfoVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PreviewFileService.java
 * @Description:
 * @date 2023/9/26 16:52
 */

@Service("portalFile.previewFileService.1")
@ApiRequestObject(value = "预览文件地址", name = "previewFileService", groups = {"管理端-文件服务"}, params = {
        @ApiParamMeta(key = "fileId", desc = "文件ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "filePath",desc = "预览地址",type = String.class),
})
public class PreviewFileService implements IBusinessService {

    @Resource
    private PortalFileService portalFileService;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String fileId = context.getValueObject(String.class, "fileId");
        String filePath = null;
        //获取合同信息
        if (StringUtil.isNotEmpty(fileId)) {
            // 获取协议预览地址
            FileInfoVo fileInfo = portalFileService.getFileInfo(fileId);
            filePath = fileInfo.getPreviewUrl();
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("filePath", filePath);
    }
}