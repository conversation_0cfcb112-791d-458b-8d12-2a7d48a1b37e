package com.cottoneasy.portal.file.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.sinosoft.dandelion.fms.base.FileInfoVo;
import com.sinosoft.dandelion.fms.proxy.FileMetaProxy;
import com.sinosoft.dandelion.fms.proxy.FileProxy;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PortalFileService.java
 * @Description:
 * @date 2023/9/26 16:40
 */

@Service
@Slf4j
public class PortalFileService {

    @Resource
    private FileProxy fileProxy;

    @Resource
    private FileMetaProxy fileMetaProxy;

    //默认系统类路径
    private static final String ClassPathPreFix="classpath:";

    /**
     * 上传文件
     * @param file	文件
     * @param fileName	文件名称
     * @param folderName	文件夹名称
     * @return
     */
    public Long uploadFile(File file, String fileName, String folderName) {
        Long uploadFileId = null;
        FileInputStream fileInputStream;
        if(StringUtil.isNotEmpty(file)) {
            try {
                fileInputStream = new FileInputStream(file);
                uploadFileId = fileProxy.upload(fileInputStream,fileName, folderName);
            } catch (FileNotFoundException e) {
                log.error("文件接收失败，失败原因：{}" , e.getMessage());
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE , "文件接收失败！");
            }
        }
        return uploadFileId;
    }

    /**
     * 上传文件
     * @param bytes	文件
     * @param fileName	文件名称
     * @param folderName	文件夹名称
     * @return
     */
    public Long uploadFile(byte[] bytes,String fileName,String folderName) {
        Long uploadFileId = null;
        uploadFileId = fileProxy.upload(bytes,fileName, folderName);
        return uploadFileId;
    }

    /**
     * 上传文件
     * @param inputStream	文件
     * @param fileName	文件名称
     * @param folderName	文件夹名称
     * @return
     */
    public Long uploadFile(InputStream inputStream, String fileName, String folderName) {
        Long uploadFileId  = null;
        if(StringUtil.isNotEmpty(inputStream)) {
            uploadFileId = fileProxy.upload(inputStream,fileName, folderName);
        }
        return uploadFileId;
    }

    /**
     * 文件删除操作
     * @param fileId 文件ID
     * @return
     */
    public boolean deleteFile(Long fileId) {
        if(fileId ==null) return false;
        boolean delete = fileMetaProxy.delete(String.valueOf(fileId));
        return delete;
    }

    /**
     * 获取文件信息
     * @param repFileId 文件ID
     * @return
     */
    public FileInfoVo getFileInfo(String repFileId){
        List<FileInfoVo> fileInfoMetas = fileMetaProxy.getFileInfoMetas(repFileId);
        FileInfoVo fileInfo = new FileInfoVo();
        if (fileInfoMetas.size()>0){
            fileInfo = fileInfoMetas.get(0);
        }
        return fileInfo;
    }

    /**
     * 获取文件信息
     * @param repFileIds 文件ID
     * @return
     */
    public Map<String, List<FileInfoVo>> getFileInfoMap(String repFileIds){
        return fileMetaProxy.getFileInfoMetaList(repFileIds);
    }


    /**
     * 单文件下载文件
     * @param repFileId 文件ID
     * @param fileName 文件名称
     * @return
     */
    public String downloadFile(String repFileId,String fileName) {
        String resultFilePath = "";
        //下载文件
        byte[] filebyte = fileProxy.download(repFileId);
        //创建临时路径
        String currentTimeMillis = System.currentTimeMillis()+"";
        String filePath = this.getClassPathStaticFile()+File.separator+currentTimeMillis+File.separator+fileName;
        File file = new File(filePath);
        FileUtil.writeBytes(filebyte, file);
        resultFilePath = filePath;
        return resultFilePath;
    }

    /**
     * 单文件下载文件
     * @param repFileId 文件ID
     * @return
     */
    public InputStream downloadFile(String repFileId) {
        InputStream inputStream = fileProxy.downloadIns(repFileId);;
        return inputStream;
    }

    public void downloadFilesTest(String repFileIdStr, String fileName, ServiceHandlerContext context) {
        //返回的文件路径
        String resultFilePath = "";
        String resultZipPath = "";
        String[] repFileIds = repFileIdStr.split(",");
        String currentTimeMillis = System.currentTimeMillis()+"";
        for (String repFileId : repFileIds) {
            FileInfoVo fileInfo = this.getFileInfo(repFileId);
            byte[] filebyte = fileProxy.download(repFileId);
            String filePath = this.getClassPathStaticFile()+File.separator+currentTimeMillis+File.separator+fileInfo.getFileName();
            File file = new File(filePath);
            FileUtil.writeBytes(filebyte, file);
            resultFilePath = filePath;
        }
        //是否需要压缩
        if(repFileIds.length > 1) {
            if (StringUtil.isEmpty(fileName)){
                resultZipPath = this.getClassPathStaticFile()+File.separator+currentTimeMillis+".zip";
            }else {
                resultZipPath = this.getClassPathStaticFile()+fileName+".zip";
            }
            ZipUtil.zip(this.getClassPathStaticFile()+File.separator+currentTimeMillis,resultZipPath,false);
        }
        if(StringUtil.isNotEmpty(resultZipPath)) {//已打包
            this.downLoadFile(resultZipPath,context);
        }else {//未打包
            this.downLoadFile(resultFilePath,context);
        }
    }

    /**
     * 下载文件到本地
     * @param fileId
     * @return
     */
    public byte[] getFile(String fileId) {
        return fileProxy.download(fileId);
    }


    /**
     * 获取临时文件路径
     * @return
     */
    public String getClassPathStaticFile() {
        try {
            String filePath = "";
            if(StringUtil.isEmpty(filePath)) {
                filePath = ResourceUtils.getURL(ClassPathPreFix).getPath();
            }else {
                filePath = ResourceUtils.getURL(ClassPathPreFix).getPath();
            }
            return filePath;
        }catch(Exception e) {
            log.error("获取路径错误：",e);
            return null;
        }
    }

    public void downLoadFile(String resultZipPath,ServiceHandlerContext context){
        HttpServletResponse response = context.getHttpServletResponse();
        ServletOutputStream out = null;
        InputStream inputStream=null;
        try {
            inputStream = new FileInputStream(resultZipPath);
            //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            out = response.getOutputStream();
            // 读取文件流
            int len = 0;
            byte[] buffer = new byte[1024 * 10];
            while ((len = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (Exception e){
            log.error("流关闭错误",e);
        } finally {
            if(inputStream!=null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("流关闭错误",e);
                }
            }
            if(out!=null) {
                try {
                    out.flush();
                    out.close();
                } catch (IOException e) {
                    log.error("流关闭错误",e);
                }
            }
        }
    }
}
