package com.cottoneasy.portal.dateconfirmform.service;

import com.cottoneasy.portal.base.entity.CustomDateConfirmFormEntity;
import com.cottoneasy.portal.base.mappers.CustomDateConfirmFormMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("portal.queryCustomDateConfirmFormById.1")
@ApiRequestObject(value = "查询表单详情", name = "QueryCustomDateConfirmFormById", groups = {"表单服务"}, params = {
        @ApiParamMeta(key = "id", desc = "主键id", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", desc = "返回结果", type = String.class)
})
public class QueryCustomDateConfirmFormById implements IBusinessService {

    @Resource
    private CustomDateConfirmFormMapper dateConfirmForm;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String id = context.getValueObject(String.class, "id");

        CustomDateConfirmFormEntity entity = dateConfirmForm.selectById(id);

        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", entity);
    }
}
