package com.cottoneasy.portal.dateconfirmform.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.CustomDateConfirmFormEntity;
import com.cottoneasy.portal.base.mappers.CustomDateConfirmFormMapper;
import com.cottoneasy.portal.dateconfirmform.vo.ConfirmFormVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("portal.queryAllCustomDateConfirmForm.1")
@ApiRequestObject(value = "查询所有表单", name = "QueryAllCustomDateConfirmForm", groups = {"表单服务"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "searchVo", desc = "列表查询参数", type = ConfirmFormVo.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", desc = "返回结果", type = String.class)
})
public class QueryAllCustomDateConfirmForm implements IBusinessService {

    @Resource
    private CustomDateConfirmFormMapper dateConfirmForm;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        ConfirmFormVo searchVo = context.getValueObject(ConfirmFormVo.class, "searchVo");
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");

        // 分页查询
        PipPagination<CustomDateConfirmFormEntity> page = new PipPagination<>(pageParameter);
        LambdaQueryWrapper<CustomDateConfirmFormEntity> wrapper = Wrappers.lambdaQuery();
        if (StringUtil.isNotEmpty(searchVo.getCustomeCode())){
            wrapper.like(CustomDateConfirmFormEntity::getCustomeCode,searchVo.getCustomeCode());
        }
        if (StringUtil.isNotEmpty(searchVo.getCustomeName())){
            wrapper.like(CustomDateConfirmFormEntity::getCustomeName,searchVo.getCustomeName());
        }
        if (StringUtil.isNotEmpty(searchVo.getConfirmed())){
            wrapper.eq(CustomDateConfirmFormEntity::getConfirmed,searchVo.getConfirmed());
        }
        if (StringUtil.isNotEmpty(searchVo.getOfficeCode())){
            wrapper.eq(CustomDateConfirmFormEntity::getOfficeCode,searchVo.getOfficeCode());
        }
        page = dateConfirmForm.selectPage(page,wrapper);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", page);


    }
}
