package com.cottoneasy.portal.dateconfirmform.service;

import com.cottoneasy.portal.base.entity.CustomDateConfirmFormEntity;
import com.cottoneasy.portal.base.mappers.CustomDateConfirmFormMapper;
import com.sinosoft.dandelion.system.client.EngineService;
import com.sinosoft.dandelion.system.client.model.Custom;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service("portal.updateCustomDateConfirmForm.1")
@ApiRequestObject(value = "更新表单", name = "UpdateCustomDateConfirmForm", groups = {"表单服务"}, params = {
        @ApiParamMeta(key = "customDateConfirmForm", desc = "表单实体", type = CustomDateConfirmFormEntity.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", desc = "返回结果", type = String.class)
})
public class UpdateCustomDateConfirmForm implements IBusinessService {

    @Resource
    private CustomDateConfirmFormMapper dateConfirmForm;


    @Override
    public void doVerify(ServiceHandlerContext context) {
        CustomDateConfirmFormEntity data = context.getValueObject(CustomDateConfirmFormEntity.class, "customDateConfirmForm");
//        if (StringUtil.isEmpty(data.getGroupAccountBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写集团账户余额！");
//        }
//        if (StringUtil.isEmpty(data.getGroupAccountLoanBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写集团账户贷款金额！");
//        }
        if (StringUtil.isEmpty(data.getGroupAccountConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写集团账户确认结果！");
        }
//        if (StringUtil.isEmpty(data.getGroupAccountConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写集团账户确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getEcommerceAccountBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写电商账户余额！");
//        }
//        if (StringUtil.isEmpty(data.getEcommerceAccountLoanBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写电商账户贷款金额！");
//        }
        if (StringUtil.isEmpty(data.getEcommerceAccountConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写电商账户确认结果！");
        }
//        if (StringUtil.isEmpty(data.getEcommerceAccountConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写电商账户确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getLogisticsAccountBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写配送账户余额！");
//        }
//        if (StringUtil.isEmpty(data.getLogisticsAccountLoanBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写配送账户贷款金额！");
//        }
        if (StringUtil.isEmpty(data.getLogisticsAccountConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写配送账户确认结果！");
        }
//        if (StringUtil.isEmpty(data.getLogisticsAccountConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写配送账户确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getInternalTradeAccountBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写国贸账户余额！");
//        }
//        if (StringUtil.isEmpty(data.getInternalTradeAccountLoanBalance())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写国贸账户贷款金额！");
//        }
        if (StringUtil.isEmpty(data.getInternalTradeAccountConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写国贸账户确认结果！");
        }
//        if (StringUtil.isEmpty(data.getInternalTradeAccountConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写国贸账户确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getTradeBatchNum())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写交易批数！");
//        }
//        if (StringUtil.isEmpty(data.getTradeWeight())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写交易重量！");
//        }
        if (StringUtil.isEmpty(data.getTradeConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写交易确认结果！");
        }
//        if (StringUtil.isEmpty(data.getTradeConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写交易确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getFinanceBatchNum())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写融资批数！");
//        }
//        if (StringUtil.isEmpty(data.getFinanceWeight())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写融资重量！");
//        }
        if (StringUtil.isEmpty(data.getFinanceConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写融资确认结果！");
        }
//        if (StringUtil.isEmpty(data.getFinanceConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写融资确认详情！");
//        }
//        if (StringUtil.isEmpty(data.getSupervisionBatchNum())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写监管批数！");
//        }
//        if (StringUtil.isEmpty(data.getSupervisionWeight())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写监管重量！");
//        }
        if (StringUtil.isEmpty(data.getSupervisionConfirmResult().toString())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写监管确认结果！");
        }
//        if (StringUtil.isEmpty(data.getSupervisionConfirmDesc())){
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写监管确认详情！");
//        }
        if (StringUtil.isEmpty(data.getChecker())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写核对人！");
        }
        if (StringUtil.isEmpty(data.getContantNum())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "请填写联系电话！");
        }
    }


    @Override
    public void doWork(ServiceHandlerContext context) {
        CustomDateConfirmFormEntity data = context.getValueObject(CustomDateConfirmFormEntity.class, "customDateConfirmForm");
        String currentUserId = context.getCurrentUserId();
        String currentUserRealName = context.getCurrentUserRealName();
        Custom custom = EngineService.getCustomService().getCustom(currentUserId);
        if (data.getGroupAccountConfirmResult().equals(-1) || data.getLogisticsAccountConfirmResult().equals(-1)
        || data.getEcommerceAccountConfirmResult().equals(-1) || data.getInternalTradeAccountConfirmResult().equals(-1)
        || data.getTradeConfirmResult().equals(-1) || data.getFinanceConfirmResult().equals(-1)
        || data.getSupervisionConfirmResult().equals(-1)){
            data.setConfirmed(-1);
        }else {
            data.setConfirmed(1);
        }
        data.setConfirmDate(new Date());
        data.setConfirmUserId(currentUserId);
        data.setConfirmUserName(currentUserRealName);
        if(custom != null){
            data.setOfficeCode(custom.getAttachmentCode());
            data.setOfficeName(custom.getAttachmentName());
        }
        if (StringUtil.isEmpty(data.getId())){
            String currentUserCustomCode = context.getCurrentUserCustomCode();
            String currentUserCustomName = context.getCurrentUserCustomName();
            data.setCustomeCode(currentUserCustomCode);
            data.setCustomeName(currentUserCustomName);
            data.setCreateUserId(currentUserId);
            data.setCreateUserName(currentUserRealName);
            data.setCreateTime(new Date());
            data.setUpdateUserId(currentUserId);
            data.setUpdateUserName(currentUserRealName);
            data.setUpdateTime(new Date());
            dateConfirmForm.insert(data);
        } else {
        data.setUpdateUserId(currentUserId);
        data.setUpdateUserName(currentUserRealName);
        data.setUpdateTime(new Date());
        dateConfirmForm.updateById(data);
        }
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", "更新成功！");
    }
}
