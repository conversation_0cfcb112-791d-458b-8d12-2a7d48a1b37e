package com.cottoneasy.portal.dateconfirmform.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.CustomDateConfirmFormEntity;
import com.cottoneasy.portal.base.mappers.CustomDateConfirmFormMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("portal.queryCustomDateConfirmForm.1")
@ApiRequestObject(value = "查询表单", name = "QueryCustomDateConfirmForm", groups = {"表单服务"}, params = {
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", desc = "返回结果", type = String.class)
})
public class QueryCustomDateConfirmForm implements IBusinessService {

    @Resource
    private CustomDateConfirmFormMapper dateConfirmForm;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String userCode = context.getCurrentUserCustomCode();
        LambdaQueryWrapper<CustomDateConfirmFormEntity> Wrapper = Wrappers.lambdaQuery(CustomDateConfirmFormEntity.class);
        Wrapper.eq(CustomDateConfirmFormEntity::getCustomeCode,userCode);
        CustomDateConfirmFormEntity entity = dateConfirmForm.selectOne(Wrapper);
        if (entity == null) {
            CustomDateConfirmFormEntity formEntity = new CustomDateConfirmFormEntity();
            formEntity.setConfirmed(-1);
            this.createSuccessResponse(context);
            context.getResponseBody().getDataSet().put("result", formEntity);
        }else {
            this.createSuccessResponse(context);
            context.getResponseBody().getDataSet().put("result", entity);
        }

    }
}
