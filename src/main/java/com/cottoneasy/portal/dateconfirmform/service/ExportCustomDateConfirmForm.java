package com.cottoneasy.portal.dateconfirmform.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.CustomDateConfirmFormEntity;
import com.cottoneasy.portal.base.mappers.CustomDateConfirmFormMapper;
import com.cottoneasy.portal.login.utils.LoginStyleUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.harry.dandelion.framework.core.SystemBootstrap;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service("portal.exportCustomDateConfirmForm.1")
@ApiRequestObject(value = "导出表单", name = "ExportCustomDateConfirmForm", groups = {"表单服务"}, params = {
        @ApiParamMeta(key = "ids", desc = "主键id", type = String.class),
        @ApiParamMeta(key = "customeCode", desc = "客户编码", type = String.class),
        @ApiParamMeta(key = "customeName", desc = "客户名称", type = String.class),
        @ApiParamMeta(key = "officeName", desc = "办事处名称", type = String.class),
        @ApiParamMeta(key = "confirmed", desc = "是否确认", type = Integer.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result", desc = "返回结果", type = String.class)
})
public class ExportCustomDateConfirmForm implements IBusinessService {

    @Resource
    private CustomDateConfirmFormMapper dateConfirmForm;

    @Resource
    private SystemBootstrap bootstrap;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ids = context.getValueObject(String.class, "ids");
        String customeCode = context.getValueObject(String.class, "customeCode");
        String customeName = context.getValueObject(String.class, "customeName");
        String officeCode = context.getValueObject(String.class, "officeCode");
        Integer confirmed = context.getValueObject(Integer.class, "confirmed");

        LambdaQueryWrapper<CustomDateConfirmFormEntity> wrapper = Wrappers.lambdaQuery();

        if (StringUtil.isNotEmpty(ids)){
            wrapper.in(CustomDateConfirmFormEntity::getId, Arrays.asList(ids.split(",")));
        }
        if (StringUtil.isNotEmpty(customeCode)){
            wrapper.like(CustomDateConfirmFormEntity::getCustomeCode,customeCode);
        }
        if (StringUtil.isNotEmpty(customeName)){
            wrapper.like(CustomDateConfirmFormEntity::getCustomeName,customeName);
        }
        if (StringUtil.isNotEmpty(officeCode)){
            wrapper.eq(CustomDateConfirmFormEntity::getOfficeCode,officeCode);
        }
        if (confirmed!=null){
            if (!confirmed.equals(0)) {
                wrapper.eq(CustomDateConfirmFormEntity::getConfirmed, confirmed);
            }
        }

        List<CustomDateConfirmFormEntity> entities = dateConfirmForm.selectList(wrapper);

        //标题
        String[] array = {"ID","客户代码","客户名称","隶属办事处代码","办事处名称","是否已确认","确认时间","确认人","确认人ID"
                ,"核对人","联系电话","集团账户余额","集团账户贷款金额","集团账户确认结果","集团账户确认详情"
                ,"电商账户余额","电商账户贷款金额","电商账户确认结果","电商账户确认详情","配送账户余额","配送账户贷款金额"
                ,"配送账户确认结果","配送账户确认详情","国贸账户余额","国贸账户贷款金额","国贸账户确认结果","国贸账户确认详情"
                ,"交易批数","交易重量","交易确认结果","交易确认详情","融资批数","融资重量","融资确认结果","融资确认详情"
                ,"监管批数","监管重量","监管确认结果","监管确认详情","创建人","创建人ID","创建时间","更新人","更新人ID","更新时间"};
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet("交易商表单");
        CellStyle titleStyle = LoginStyleUtil.createStyleForTitle(wb);
        //标题
        Row row = sheet.createRow((int) 0);
        for (int i = 0; i < array.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(array[i]);
            cell.setCellStyle(titleStyle);
        }
        //数据
        int index = 1;
        CellStyle cellStyle = LoginStyleUtil.createStyleForCell(wb);
        cellStyle.setWrapText(true);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < entities.size(); i++) {
            XSSFRow row0 = sheet.createRow(index);
            index++;
            CustomDateConfirmFormEntity entity = entities.get(i);
            for (int j = 0; j < array.length; j++) {
                XSSFCell cell = row0.createCell(j);
                String exportKey = array[j];
                switch (exportKey) {
                    case "ID":
                        cell.setCellValue(entity.getId());
                        break;
                    case "客户代码":
                        cell.setCellValue(entity.getCustomeCode());
                        break;
                    case "客户名称":
                        cell.setCellValue(entity.getCustomeName());
                        break;
                    case "隶属办事处代码":
                        cell.setCellValue(entity.getOfficeCode());
                        break;
                    case "办事处名称":
                        cell.setCellValue(entity.getOfficeName());
                        break;
                    case "是否已确认":
                        if (entity.getConfirmed().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getConfirmed().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getConfirmed().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "确认时间":
                        if (entity.getConfirmDate() != null){
                            cell.setCellValue(dateFormat.format(entity.getConfirmDate()));
                        }
                        break;
                    case "确认人":
                        cell.setCellValue(entity.getConfirmUserName());
                        break;
                    case "确认人ID":
                        cell.setCellValue(entity.getConfirmUserId());
                        break;
                    case "核对人":
                        cell.setCellValue(entity.getChecker());
                        break;
                    case "联系电话":
                        cell.setCellValue(entity.getContantNum());
                        break;
                    case "集团账户余额":
                        cell.setCellValue(entity.getGroupAccountBalance());
                        break;
                    case "集团账户贷款金额":
                        cell.setCellValue(entity.getGroupAccountLoanBalance());
                        break;
                    case "集团账户确认结果":
                        if (entity.getGroupAccountConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getGroupAccountConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getGroupAccountConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "集团账户确认详情":
                        cell.setCellValue(entity.getGroupAccountConfirmDesc());
                        break;
                    case "电商账户余额":
                        cell.setCellValue(entity.getEcommerceAccountBalance());
                        break;
                    case "电商账户贷款金额":
                        cell.setCellValue(entity.getEcommerceAccountLoanBalance());
                        break;
                    case "电商账户确认结果":
                        if (entity.getEcommerceAccountConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getEcommerceAccountConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getEcommerceAccountConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "电商账户确认详情":
                        cell.setCellValue(entity.getEcommerceAccountConfirmDesc());
                        break;
                    case "配送账户余额":
                        cell.setCellValue(entity.getLogisticsAccountBalance());
                        break;
                    case "配送账户贷款金额":
                        cell.setCellValue(entity.getLogisticsAccountLoanBalance());
                        break;
                    case "配送账户确认结果":
                        if (entity.getLogisticsAccountConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getLogisticsAccountConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getLogisticsAccountConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "配送账户确认详情":
                        cell.setCellValue(entity.getLogisticsAccountConfirmDesc());
                        break;
                    case "国贸账户余额":
                        cell.setCellValue(entity.getInternalTradeAccountBalance());
                        break;
                    case "国贸账户贷款金额":
                        cell.setCellValue(entity.getInternalTradeAccountLoanBalance());
                        break;
                    case "国贸账户确认结果":
                        if (entity.getInternalTradeAccountConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getInternalTradeAccountConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getInternalTradeAccountConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "国贸账户确认详情":
                        cell.setCellValue(entity.getInternalTradeAccountConfirmDesc());
                        break;
                    case "交易批数":
                        cell.setCellValue(entity.getTradeBatchNum());
                        break;
                    case "交易重量":
                        cell.setCellValue(entity.getTradeWeight());
                        break;
                    case "交易确认结果":
                        if (entity.getTradeConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getTradeConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getTradeConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "交易详情":
                        cell.setCellValue(entity.getTradeConfirmDesc());
                        break;
                    case "融资批数":
                        cell.setCellValue(entity.getFinanceBatchNum());
                        break;
                    case "融资重量":
                        cell.setCellValue(entity.getFinanceWeight());
                        break;
                    case "融资确认结果":
                        if (entity.getFinanceConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getFinanceConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getFinanceConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "融资确认详情":
                        cell.setCellValue(entity.getFinanceConfirmDesc());
                        break;
                    case "监管批数":
                        cell.setCellValue(entity.getSupervisionBatchNum());
                        break;
                    case "监管重量":
                        cell.setCellValue(entity.getSupervisionWeight());
                        break;
                    case "监管确认结果":
                        if (entity.getSupervisionConfirmResult().equals(-1)){
                            cell.setCellValue("有异议");
                        }else if (entity.getSupervisionConfirmResult().equals(0)){
                            cell.setCellValue("未确认");
                        }else if (entity.getSupervisionConfirmResult().equals(1)){
                            cell.setCellValue("已确认");
                        }
                        break;
                    case "监管确认详情":
                        cell.setCellValue(entity.getSupervisionConfirmDesc());
                        break;
                    case "创建人":
                        cell.setCellValue(entity.getCreateUserName());
                        break;
                    case "创建人ID":
                        cell.setCellValue(entity.getCreateUserId());
                        break;
                    case "创建时间":
                        if (entity.getCreateTime()!=null){
                            cell.setCellValue(dateFormat.format(entity.getCreateTime()));
                        }
                        break;
                    case "更新人":
                        cell.setCellValue(entity.getUpdateUserName());
                        break;
                    case "更新人ID":
                        cell.setCellValue(entity.getUpdateUserId());
                        break;
                    case "更新时间":
                        if (entity.getUpdateTime() != null){
                            cell.setCellValue(dateFormat.format(entity.getUpdateTime()));
                        }
                        break;
                }
                cell.setCellStyle(cellStyle);
            }
        }
        LoginStyleUtil.autoSizeColumn(sheet,array.length);
        String path = bootstrap.getExportPath() + File.separator + "交易商表单统计--" + new Date().getTime() + ".xlsx";
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(path);
            wb.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("文件导出失败：{}", e);
        }finally {
            try {
                wb.close();
                out.close();
            } catch (IOException e) {
                log.error("文件导出失败：{}", e);
            }
        }

        this.createSuccessResponse(context);
        context.getResponseBody().setData("FilePath", path);

    }
}
