package com.cottoneasy.portal.base.mappers;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cottoneasy.portal.base.entity.CmsComplainEntity;
import com.cottoneasy.portal.complain.vo.ComplainParamVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

public interface CmsComplainMapper extends BaseMapper<CmsComplainEntity> {
    PipPagination<ComplainParamVo> selectComplainPage(@Param("page") PipPagination<ComplainParamVo> page, @Param("ew") LambdaQueryWrapper<CmsComplainEntity> queryWrapper);
}
