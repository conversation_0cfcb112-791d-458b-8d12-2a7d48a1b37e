package com.cottoneasy.portal.base.mappers;

import com.cottoneasy.portal.base.entity.CmsPurchaseEntity;
import com.cottoneasy.portal.purchase.vo.PurchaseParamVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface CmsPurchaseMapper extends BaseMapper<CmsPurchaseEntity> {

    PipPagination<PurchaseParamVo> selectPurchasePage(@Param("page") PipPagination<PurchaseParamVo> page, @Param("searchVo") PurchaseParamVo purchaseParamVo);



}
