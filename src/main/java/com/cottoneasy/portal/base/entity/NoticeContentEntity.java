package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通知内容信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@TableName("de_sys_notice_content")
@ApiModel(value="NoticeContentEntity对象", description="通知内容信息")
@NoArgsConstructor
public class NoticeContentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置补充表id")
    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "业务通知主表id")
    @TableField("BIZ_MESSAGE_ID")
    private Long bizMessageId;

    @ApiModelProperty(value = "内容标题")
    @TableField("POPUP_FILED_KEY")
    private String popupFiledKey;

    @ApiModelProperty(value = "内容值")
    @TableField("POPUP_FILED_VALUE")
    private String popupFiledValue;

    @ApiModelProperty(value = "链接地址")
    @TableField("A_HREF_URL")
    private String aHrefUrl;

    @ApiModelProperty(value = "排序值")
    @TableField("CONTENT_SEQ")
    private Integer contentSeq;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty(value = "更新人ID")
    @TableField("UPDATE_USER_ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("NOTE")
    private String note;

    public NoticeContentEntity (String popupFiledKey, String popupFiledValue) {
        this.popupFiledKey = popupFiledKey;
        this.popupFiledValue = popupFiledValue;
    }

    public NoticeContentEntity (String popupFiledKey, String popupFiledValue, String aHrefUrl) {
        this.popupFiledKey = popupFiledKey;
        this.popupFiledValue = popupFiledValue;
        this.aHrefUrl = aHrefUrl;
    }


}
