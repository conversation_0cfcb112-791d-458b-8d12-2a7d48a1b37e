package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务通知配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("de_sys_notice_biz")
@ApiModel(value="NoticeBizEntity对象", description="业务通知配置")
public class NoticeBizEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "业务通知编码，自定义，全局唯一")
    @TableField("NOTICE_BIZ_CODE")
    private String noticeBizCode;

    @ApiModelProperty(value = "通知配置ID")
    @TableField("NOTICE_CONFIG_ID")
    private Long noticeConfigId;

    @ApiModelProperty(value = "图标")
    @TableField("NOTICE_ICON")
    private String noticeIcon;

    @ApiModelProperty(value = "标题")
    @TableField("NOTICE_TITLE")
    private String noticeTitle;

    @ApiModelProperty(value = "优先级")
    @TableField("NOTICE_PRIORITY")
    private Integer noticePriority;

    @ApiModelProperty(value = "通知处理形式 1、阅读形式 2、业务形式")
    @TableField("NOTICE_HANDLE_TYPE")
    private Integer noticeHandleType;

    @ApiModelProperty(value = "唤醒方式：0、不唤醒(仅在登录提醒)，1-定时提醒，2、特定路径请求唤醒，3、全局任意请求提醒")
    @TableField("REMIND_TYPE")
    private Integer remindType;

    @ApiModelProperty(value = "定时时间（分钟）")
    @TableField("REMIND_MINUTES")
    private Integer remindMinutes;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty(value = "更新人ID")
    @TableField("UPDATE_USER_ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("NOTE")
    private String note;


}
