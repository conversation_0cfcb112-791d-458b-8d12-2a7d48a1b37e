package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sinosoft.dandelion.system.client.params.DataItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: UserLoginAgreeRecord
 * @Description: 用户登录同意协议记录
 * <AUTHOR>
 * @date 2023年2月4日 上午10:00:37
 *
 */
@Data
@TableName(value ="DE_USER_LOGIN_AGREE_RECORD")
public class UserLoginAgreeRecord implements Serializable {

	private static final long serialVersionUID = -1422066551765717410L;

	@TableId(value = "ID",type = IdType.AUTO)
	@ApiModelProperty("ID")
	private Integer id;

	@TableField(value = "AGREEMENT_ID")
	@ApiModelProperty("协议ID")
	private Integer agreementId;

	@ApiModelProperty("企业ID")
	@TableField(value = "enterprise_id")
	private String enterpriseId;
	
	@ApiModelProperty("客户ID")
	@TableField(value = "custom_id")
	private String customId;

	@ApiModelProperty("客户类型")
	@TableField(value = "CUSTOM_TYPE")
	private DataItem customType;
	
	@TableField(value = "USER_ID")
	@ApiModelProperty("用户ID")
	private String userId;
	
	@ApiModelProperty("登录账号")
	@TableField(value = "LOGIN_NAME")
	private String loginName;
	
	@ApiModelProperty("客户代码")
	@TableField(value = "custom_code")
	private String customCode;

	@ApiModelProperty("创建时间")
	@TableField(value = "create_Time",fill = FieldFill.INSERT)
	private Date createTime;

	@ApiModelProperty("更新时间")
	@TableField(value = "update_Time",fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}
