package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("T_PORTAL_CUSTOM_DATA_CONFIRM_FORM")
public class CustomDateConfirmFormEntity implements Serializable {
   
	private static final long serialVersionUID = -1288066898419804648L;
	/**
     * ID
     */
    @TableId("ID")
    private String Id;
    /**
     * 办事处代码
     */
    @TableField("OFFICE_CODE")
    private String officeCode;
    /**
     * 办事处名称
     */
    @TableField("OFFICE_NAME")
    private String officeName;
    /**
     * CUSTOME_CODE
     */
    @TableField("CUSTOME_CODE")
    private String customeCode;
    /**
     * CUSTOME_NAME
     */
    @TableField("CUSTOME_NAME")
    private String customeName;
    /**
     * 是否已确认
     */
    @TableField("CONFIRMED")
    private Integer confirmed;
    /**
     * 确认时间
     */
    @TableField("CONFIRM_DATE")
    private Date confirmDate;
    /**
     * 确认人
     */
    @TableField("CONFIRM_USER_NAME")
    private String confirmUserName;
    /**
     * 确认人ID
     */
    @TableField("CONFIRM_USER_ID")
    private String confirmUserId;
    /**
     * 核对人
     */
    @TableField("CHECKER")
    private String checker;
    /**
     * 联系电话
     */
    @FieldEncrypt
    @TableField("CONTACT_NUM")
    private String contantNum;
    /**
     * 集团账户余额
     */
    @TableField("GROUP_ACCOUNT_BALANCE")
    private String groupAccountBalance;
    /**
     * 集团账户贷款金额
     */
    @TableField("GROUP_ACCOUNT_LOAN_BALANCE")
    private String groupAccountLoanBalance;
    /**
     * 集团账户确认结果
     */
    @TableField("GROUP_ACCOUNT_CONFIRM_RESULT")
    private Integer groupAccountConfirmResult;
    /**
     * 集团账户确认详情
     */
    @TableField("GROUP_ACCOUNT_CONFIRM_DESC")
    private String groupAccountConfirmDesc;
    /**
     * 电商账户余额
     */
    @TableField("ECOMMERCE_ACCOUNT_BALANCE")
    private String ecommerceAccountBalance;
    /**
     * 电商账户贷款金额
     */
    @TableField("ECOMMERCE_ACCOUNT_LOAN_BALANCE")
    private String ecommerceAccountLoanBalance;
    /**
     * 电商账户确认结果
     */
    @TableField("ECOMMERCE_ACCOUNT_CONFIRM_RESULT")
    private Integer ecommerceAccountConfirmResult;
    /**
     * 电商账户确认详情
     */
    @TableField("ECOMMERCE_ACCOUNT_CONFIRM_DESC")
    private String ecommerceAccountConfirmDesc;
    /**
     * 配送账户余额
     */
    @TableField("LOGISTICS_ACCOUNT_BALANCE")
    private String logisticsAccountBalance;
    /**
     * 配送账户贷款金额
     */
    @TableField("LOGISTICS_ACCOUNT_LOAN_BALANCE")
    private String logisticsAccountLoanBalance;
    /**
     * 配送账户确认结果
     */
    @TableField("LOGISTICS_ACCOUNT_CONFIRM_RESULT")
    private Integer logisticsAccountConfirmResult;

    /**
     * 配送账户确认详情
     */
    @TableField("LOGISTICS_ACCOUNT_CONFIRM_DESC")
    private String logisticsAccountConfirmDesc;
    /**
     * 国贸账户余额
     */
    @TableField("INTERNAL_TRADE_ACCOUNT_BALANCE")
    private String internalTradeAccountBalance;
    /**
     * 国贸账户贷款金额
     */
    @TableField("INTERNAL_TRADE_ACCOUNT_LOAN_BALANCE")
    private String internalTradeAccountLoanBalance;
    /**
     * 国贸账户确认结果
     */
    @TableField("INTERNAL_TRADE_ACCOUNT_CONFIRM_RESULT")
    private Integer internalTradeAccountConfirmResult;
    /**
     * 国贸账户确认详情
     */
    @TableField("INTERNAL_TRADE_ACCOUNT_CONFIRM_DESC")
    private String internalTradeAccountConfirmDesc;
    /**
     * 交易批数
     */
    @TableField("TRADE_BATCH_NUM")
    private String tradeBatchNum;
    /**
     * 交易重量
     */
    @TableField("TRADE_WEIGHT")
    private String tradeWeight;
    /**
     * 交易确认结果
     */
    @TableField("TRADE_CONFIRM_RESULT")
    private Integer tradeConfirmResult;
    /**
     * 交易确认详情
     */
    @TableField("TRADE_CONFIRM_DESC")
    private String tradeConfirmDesc;
    /**
     * 融资批数
     */
    @TableField("FINANCE_BATCH_NUM")
    private String financeBatchNum;
    /**
     * 融资重量
     */
    @TableField("FINANCE_WEIGHT")
    private String financeWeight;
    /**
     * 融资确认结果
     */
    @TableField("FINANCE_CONFIRM_RESULT")
    private Integer financeConfirmResult;
    /**
     * 融资确认详情
     */
    @TableField("FINANCE_CONFIRM_DESC")
    private String financeConfirmDesc;
    /**
     * 监管批数
     */
    @TableField("SUPERVISION_BATCH_NUM")
    private String supervisionBatchNum;
    /**
     * 监管重量
     */
    @TableField("SUPERVISION_WEIGHT")
    private String supervisionWeight;
    /**
     * 监管确认结果
     */
    @TableField("SUPERVISION_CONFIRM_RESULT")
    private Integer supervisionConfirmResult;
    /**
     * 监管确认详情
     */
    @TableField("SUPERVISION_CONFIRM_DESC")
    private String supervisionConfirmDesc;
    /**
     * 创建人
     */
    @TableField("CREATE_USER_NAME")
    private String createUserName;
    /**
     * 创建人ID
     */
    @TableField("CRTATE_USER_ID")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;
    /**
     * 更新人ID
     */
    @TableField("UPDATE_USER_ID")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;



}
