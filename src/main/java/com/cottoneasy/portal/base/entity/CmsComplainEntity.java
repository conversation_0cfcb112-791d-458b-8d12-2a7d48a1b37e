package com.cottoneasy.portal.base.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

/**
 * 投诉信息表
 * @TableName DE_CMS_COMPLAIN
 */
@Data
@TableName(value ="DE_CMS_COMPLAIN")
public class CmsComplainEntity implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 5713807624204977634L;


    @TableId(value = "COMPLAIN_ID")
    private String complainId;

    /**
     * 客户名称
     */
    @TableField(value = "CUSTOM_NAME")
    private String customName;

    /**
     * 交易商代码
     */
    @TableField(value = "CUSTOM_ID")
    private String customId;

    /**
     * 是否匿名
     */
    @TableField(value = "ANONYMOUS")
    private String anonymous;

    /**
     * 联系电话
     */
    @FieldEncrypt
    @TableField(value = "TELPHONE")
    private String telphone;

    /**
     * 投诉内容
     */
    @TableField(value = "CONTENT")
    private String content;

    /**
     * 提交时间
     */
    @TableField(value = "SUBMIT_TIME")
    private Date submitTime;

    /**
     * 处理状态（待处理，已处理）
     */
    @TableField(value = "STATUS")
    private String status;

    /**
     * 处理结果
     */
    @TableField(value = "DEAL_RESULT")
    private String dealResult;

    /**
     * 处理时间
     */
    @TableField(value = "DEAL_TIME")
    private Date dealTime;

    /**
     * 处理人id
     */
    @TableField(value = "DEAL_USER_ID")
    private String dealUserId;

    /**
     * 处理人名字
     */
    @TableField(value = "DEAL_USER_NAME")
    private String dealUserName;


}
