package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @ClassName: UserLoginAgreement
 * @Description: 用户登录协议
 * <AUTHOR>
 * @date 2023年2月4日 上午9:29:25
 *
 */
@Data
@TableName(value ="DE_USER_LOGIN_AGREEMENT")
public class UserLoginAgreement implements Serializable{

	private static final long serialVersionUID = -3513780110839507010L;

	@TableId(value = "ID",type = IdType.AUTO)
	@ApiModelProperty("ID")
	private Integer id;
	
	@TableField("name")
	@ApiModelProperty("协议名称")
	private String displayName;
	
	@TableField("TITLE")
	@ApiModelProperty("标题")
	private String title;
	
	@TableField("CONTENT")
	@ApiModelProperty("内容")
	private String content;
	
	@TableField("SOURCE")
	@ApiModelProperty("来源")
	private String source;
	
	@TableField("AUTHOR")
	@ApiModelProperty("作者")
	private String author;
	
	@TableField("DEFAULT_AGREEMENT")
	@ApiModelProperty("默认协议 0:否   1:是 ")
	private Integer defaultAgreement;
	
	//1:交易商  2：仓库 3：物流公司 4：加工单位 5：办事处 6：监管机构 7：保险公司 8：纤检机构 9：市场 10：市场交易商 11：普通企业 12：其他
	@TableField("LIMIT_CUSTOM_TYPE")
	@ApiModelProperty("限制登录客户类型 ")
	private String limitAgreeCustomType;
	
	@TableField(value="CREATE_DATE",fill = FieldFill.INSERT)
	@ApiModelProperty("创建协议")
	private String createDate;
}
