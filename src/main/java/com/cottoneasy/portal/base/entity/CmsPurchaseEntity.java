package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * 投诉信息表
 * @TableName DE_CMS_COMPLAIN
 */
@Data
@TableName(value ="DE_CMS_PURCHASE")
public class CmsPurchaseEntity implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 7184739847876902057L;


    @TableId(value = "ID")
    private String Id;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 是否GC/绿卡棉
     */
    @TableField("CARD_COTTON")
    private Integer cardCotton;



    /**
     * 联系人
     */
    @TableField("CONTACT")
    private String contact;



    /**
     * 联系电话/QQ/微信
     */
    @FieldEncrypt
    @TableField("CONTACT_NUM")
    private String contactNum;


    /**
     * 企业名称
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_USER_ID")
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_USER")
    private String createUser;

    /**
     * 最近更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_USER_ID")
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "UPDATE_USER")
    private String updateUser;


}
