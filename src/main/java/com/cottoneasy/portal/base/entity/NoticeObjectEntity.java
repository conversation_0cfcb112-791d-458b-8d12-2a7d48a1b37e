package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通知对象表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("de_sys_notice_object")
@ApiModel(value="NoticeObjectEntity对象", description="通知对象表")
public class NoticeObjectEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "业务通知ID")
    @TableField("BIZ_MESSAGE_ID")
    private Long bizMessageId;

    @ApiModelProperty(value = "通知对象编码")
    @TableField("NOTICE_OBJECT_CODE")
    private String noticeObjectCode;

    @ApiModelProperty(value = "通知对象名称")
    @TableField("NOTICE_OBJECT_NAME")
    private String noticeObjectName;

    @ApiModelProperty(value = "通知对象类型")
    @TableField("NOTICE_OBJECT_TYPE")
    private Integer noticeObjectType;

    @ApiModelProperty(value = "处理状态:0-未处理，1-已处理")
    @TableField("NOTICE_HANDLE_STATUS")
    private Integer noticeHandleStatus;

    @ApiModelProperty(value = "提醒时间")
    @TableField("NOTICE_TIME")
    private LocalDateTime noticeTime;

    @ApiModelProperty(value = "删除状态 0-未删除，1-已删除")
    @TableField("DEL_FLAG")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty(value = "更新人ID")
    @TableField("UPDATE_USER_ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("NOTE")
    private String note;


}
