package com.cottoneasy.portal.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 通知配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("de_sys_popup_config")
@ApiModel(value="PopupConfigEntity对象", description="通知配置")
public class PopupConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置id")
    @TableId("CONFIG_ID")
    private Long configId;

    @ApiModelProperty(value = "提醒展示形式：1-友好提醒、2-强制提醒")
    @TableField("POPUP_MESSAGE_TYPE")
    private Integer popupMessageType;

    @ApiModelProperty(value = "是否可关闭：0是，1否")
    @TableField("CLOSE_STATUS")
    private Integer closeStatus;

    @ApiModelProperty(value = "通知框位置,如左上、左下、居中、右上、右下")
    @TableField("POSITION")
    private String position;

    @ApiModelProperty(value = "是否自动消失 0-是，1-否")
    @TableField("AUTO_CLOSE")
    private Integer autoClose;

    @ApiModelProperty(value = "持续时间后消失(秒)")
    @TableField("HOLD_TIME")
    private Integer holdTime;

    @ApiModelProperty(value = "是否删除 0-未删除，1-已删除")
    @TableField("DEL_FLAG")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_USER_NAME")
    private String createUserName;

    @ApiModelProperty(value = "创建人ID")
    @TableField("CREATE_USER_ID")
    private String createUserId;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_USER_NAME")
    private String updateUserName;

    @ApiModelProperty(value = "更新人ID")
    @TableField("UPDATE_USER_ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("NOTE")
    private String note;


}
