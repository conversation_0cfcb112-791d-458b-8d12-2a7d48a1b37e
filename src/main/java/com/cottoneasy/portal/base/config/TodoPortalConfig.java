package com.cottoneasy.portal.base.config;

import com.cottoneasy.portal.todo.consumer.core.QueueConfiguration;
import com.cottoneasy.portal.todo.consumer.core.RedisQueueConsumer;
import com.cottoneasy.portal.todo.consumer.core.RedisQueueConsumerContainer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7
 */
@Configuration
@Slf4j
public class TodoPortalConfig {

    private final List<RedisQueueConsumer> redisQueueConsumers;

    public TodoPortalConfig(List<RedisQueueConsumer> redisQueueConsumers) {
        this.redisQueueConsumers = redisQueueConsumers;
    }

    @Bean(name = "RedisQueueConsumerContainer", initMethod = "init", destroyMethod = "destroy")
    public RedisQueueConsumerContainer redisMqConsumerContainer(@Qualifier("todoRedisTemplate") RedisTemplate<String,Object> redisTemplate) {
        RedisQueueConsumerContainer config = new RedisQueueConsumerContainer(redisTemplate);
        redisQueueConsumers.forEach(consumer -> {
            log.debug("添加待办消息消费者: {}", consumer.getQueueName());
            config.addConsumer(QueueConfiguration.builder()
                    .queue(consumer.getQueueName())
                    .consumer(consumer)
                    .build());
        });
        return config;
    }
}
