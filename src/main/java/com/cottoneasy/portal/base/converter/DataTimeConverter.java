package com.cottoneasy.portal.base.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.utils.StringUtil;

import java.util.Date;

@SuppressWarnings("rawtypes")
public class DataTimeConverter   implements Converter<Date>{

	@Override
	public Class supportJavaTypeKey() {
		
		return String.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public Date convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) throws Exception {
		String value=cellData.toString().toString();
		if(StringUtil.isEmpty(value)) {
			return null;
		}
		return DateUtils.parseDate(value, DateUtils.YYYY_MM_DD);
	}

	@Override
	public CellData convertToExcelData(Date value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) throws Exception {
		if(value==null) {
			value=null;
		}
		 return new CellData(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, value));
	}

}
