package com.cottoneasy.portal.base.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.harry.dandelion.framework.core.utils.StringUtil;

@SuppressWarnings("rawtypes")
public class CustomStringConverter   implements Converter<String>{

	@Override
	public Class supportJavaTypeKey() {
		
		return String.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) throws Exception {
		String value=cellData.toString().toString();
		if(StringUtil.isEmpty(value)) {
			return "";
		}
		return value.replaceAll(" ","");
	}

	@Override
	public CellData convertToExcelData(String value, ExcelContentProperty contentProperty,
                                       GlobalConfiguration globalConfiguration) throws Exception {
		if(value==null) {
			value="";
		}
		 return new CellData(value);
	}

}
