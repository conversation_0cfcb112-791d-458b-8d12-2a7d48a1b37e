package com.cottoneasy.portal.base.converter;/**
 * <AUTHOR>
 * @create 2021-11-03 11:55
 */

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.sinosoft.dandelion.system.client.params.DataItem;


@SuppressWarnings("rawtypes")
public class CustomDataItemConverter implements Converter<DataItem> {

    @Override
    public Class supportJavaTypeKey() {
        return DataItem.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
        public DataItem convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        //excel格式数据转java对象
        String value= cellData.getStringValue();
        DataItem dataItem=new DataItem();
        dataItem.setCode("");
        dataItem.setValue(value);
        return dataItem;
    }



        @Override
        public CellData convertToExcelData(DataItem dataItem, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            //java对象转成excel格式数据
            String value=dataItem.getValue();
            return new CellData<>(value);
        }
}
