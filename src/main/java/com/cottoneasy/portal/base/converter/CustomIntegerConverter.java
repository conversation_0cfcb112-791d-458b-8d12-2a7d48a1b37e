package com.cottoneasy.portal.base.converter;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.harry.dandelion.framework.core.utils.StringUtil;

import java.math.BigDecimal;

@SuppressWarnings("rawtypes")
public class CustomIntegerConverter implements Converter<Integer> {


    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        BigDecimal numberValue = cellData.getNumberValue();
        if (numberValue!=null){
            return numberValue.intValue();
        }
        String value=cellData.getStringValue();
        if(StringUtil.isEmpty(value)) {
            return null;
        }
        //删除空格
        value=value.replaceAll(" ","");
        return Integer.parseInt(value);
    }

    @Override
    public CellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new CellData(NumberUtil.toStr(value));
    }
}
