package com.cottoneasy.portal.base.converter;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.harry.dandelion.framework.core.utils.StringUtil;

@SuppressWarnings("rawtypes")
public class CustomLongConverter implements Converter<Long> {


    @Override
    public Class supportJavaTypeKey() {
        return Long.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Long convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (StringUtil.isNotEmpty(value)){
            return Long.parseLong(value);
        }
        if(StringUtil.isEmpty(value)) {
            return null;
        }
        value=value.replaceAll(" ","");
        return Long.parseLong(value);
    }

    @Override
    public CellData convertToExcelData(Long value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new CellData(NumberUtil.toStr(value));
    }
}
