package com.cottoneasy.portal.base.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottoneasy.portal.base.entity.PopupConfigEntity;
import com.cottoneasy.portal.base.mappers.PopupConfigMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PopupConfigService extends ServiceImpl<PopupConfigMapper, PopupConfigEntity> {

    public List<PopupConfigEntity> getValidConfig(){
        LambdaQueryWrapper<PopupConfigEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PopupConfigEntity:: getDelFlag, 0);
        return this.list(queryWrapper);
    }
}
