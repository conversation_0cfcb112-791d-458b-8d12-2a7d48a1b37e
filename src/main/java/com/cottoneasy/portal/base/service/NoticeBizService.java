package com.cottoneasy.portal.base.service;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.mappers.NoticeBizMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NoticeBizService extends ServiceImpl<NoticeBizMapper, NoticeBizEntity> {

    public List<NoticeBizEntity> getAllConfig(){
        return this.list();
    }
}
