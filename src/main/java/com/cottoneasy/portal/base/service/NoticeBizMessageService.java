package com.cottoneasy.portal.base.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.base.entity.NoticeContentEntity;
import com.cottoneasy.portal.base.mappers.NoticeBizMessageMapper;
import com.cottoneasy.portal.base.vo.NoticeBizMessageVo;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class NoticeBizMessageService extends ServiceImpl<NoticeBizMessageMapper, NoticeBizMessageEntity> {

    @Resource
    private NoticeContentService noticeContentService;

    public List<NoticeBizMessageVo> getNoticeBizMessageVoList(Long bizConfigId) {
        List<NoticeBizMessageVo> noticeBizMessageVoList = this.getBaseMapper().getNoticeBizMessageList(bizConfigId);
        if (CollUtil.isEmpty(noticeBizMessageVoList)) {
            return new ArrayList<>();
        }
        List<List<NoticeBizMessageVo>> partitionList = Lists.partition(noticeBizMessageVoList, 1000);

        Map<Long, Map<String, List<NoticeContentEntity>>> noticeContentEntityListMap = new HashMap<>();

        LambdaQueryWrapper<NoticeContentEntity> query2 = Wrappers.lambdaQuery();
        for (List<NoticeBizMessageVo> noticeBizMessageVoSubList : partitionList) {
            List<Long> bizMessageIdList = noticeBizMessageVoSubList.stream().map(NoticeBizMessageVo::getId).collect(Collectors.toList());
            query2.clear();
            query2.in(NoticeContentEntity::getBizMessageId, bizMessageIdList);
            query2.select(NoticeContentEntity::getBizMessageId, NoticeContentEntity::getPopupFiledKey, NoticeContentEntity::getPopupFiledValue, NoticeContentEntity::getAHrefUrl);
            List<NoticeContentEntity> _ncontentList = this.noticeContentService.list(query2);
            if (!CollUtil.isEmpty(_ncontentList)) {
                Map<Long, Map<String, List<NoticeContentEntity>>> _noticeContentEntityListMap = _ncontentList.stream().collect(Collectors.groupingBy(NoticeContentEntity::getBizMessageId, Collectors.groupingBy(NoticeContentEntity::getPopupFiledKey)));
                noticeContentEntityListMap.putAll(_noticeContentEntityListMap);
            }
        }
        for (NoticeBizMessageVo noticeBizMessageVo : noticeBizMessageVoList) {
            if (noticeContentEntityListMap.containsKey(noticeBizMessageVo.getId())) {
                Map<String, List<NoticeContentEntity>> _noticeContentEntityList = noticeContentEntityListMap.get(noticeBizMessageVo.getId());
                List<NoticeContentEntity> _batchNumberList = _noticeContentEntityList.get("batchNumber");
                List<NoticeContentEntity> _bizTypeList = _noticeContentEntityList.get("bizType");
                List<NoticeContentEntity> _operateList = _noticeContentEntityList.get("operate");
                noticeBizMessageVo.setBatchNumbers(_batchNumberList.get(0).getPopupFiledValue());
                noticeBizMessageVo.setBizType(_bizTypeList.get(0).getPopupFiledValue());
                noticeBizMessageVo.setOperateTitle(_operateList.get(0).getPopupFiledValue());
                noticeBizMessageVo.setUrl(_operateList.get(0).getAHrefUrl());
            }
        }
        return noticeBizMessageVoList;
    }
}
