<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cottoneasy.portal.base.mappers.CmsComplainMapper">


	<!--    ResultMap配置参考  实际应用中，如果没有用到，请删除           -->
	<resultMap id="ServiceComplainMapping" type="com.cottoneasy.portal.base.entity.CmsComplainEntity" autoMapping="true" />

<!--	 投诉分页 -->
	<select id="selectComplainPage"  resultType="com.cottoneasy.portal.complain.vo.ComplainParamVo" resultMap="ServiceComplainMapping">
		SELECT
			*
		FROM de_cms_complain
		${ew.customSqlSegment}
		order By submit_time Desc
	</select>






</mapper>
