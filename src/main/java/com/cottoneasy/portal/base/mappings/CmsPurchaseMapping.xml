<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cottoneasy.portal.base.mappers.CmsPurchaseMapper">


	<!--    ResultMap配置参考  实际应用中，如果没有用到，请删除           -->
	<resultMap id="ServicePurchaseMapping" type="com.cottoneasy.portal.base.entity.CmsPurchaseEntity" autoMapping="true" />

<!--	  -->
	<select id="selectPurchasePage" resultType="com.cottoneasy.portal.purchase.vo.PurchaseParamVo">

		SELECT
			*
		FROM de_cms_purchase
		<where>
			<if test='searchVo.remark != null and searchVo.remark != "" '>
				and REMARK LIKE CONCAT('%',#{searchVo.remark},'%')
			</if>
			<if test='searchVo.cardCotton != null and searchVo.cardCotton != "" '>
				and CARD_COTTON = #{searchVo.cardCotton}
			</if>
			<if test='searchVo.companyName != null and searchVo.companyName != "" '>
				and COMPANY_NAME LIKE CONCAT('%',#{searchVo.companyName},'%')
			</if>
		</where>
		order By CREATE_TIME Desc

	</select>


</mapper>
