<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cottoneasy.portal.base.mappers.NoticeBizMessageMapper">

    <!-- 查询主表未处理的待办 -->
    <select id="getNoticeBizMessageList" resultType="com.cottoneasy.portal.base.vo.NoticeBizMessageVo">
        SELECT
            bm.ID,
            bm.BUSINESS_ID,
            noo.NOTICE_OBJECT_CODE as customCode,
            noo.NOTICE_OBJECT_NAME as customName
        FROM
            de_sys_notice_biz_message bm
            JOIN de_sys_notice_object noo ON bm.ID = noo.BIZ_MESSAGE_ID
        WHERE
            bm.DISPOSE_STATUS = 0
            AND bm.DEL_FLAG = 0
    </select>

    <select id="getExistBatchNumbers" resultType="java.lang.String">
        SELECT
        nco.POPUP_FILED_VALUE
        FROM
        de_sys_notice_biz_message bm
        JOIN de_sys_notice_object noo ON bm.ID = noo.BIZ_MESSAGE_ID
        JOIN de_sys_notice_content nco ON bm.ID = nco.BIZ_MESSAGE_ID
        WHERE
        bm.DISPOSE_STATUS = 0
        AND bm.DEL_FLAG = 0
        AND nco.POPUP_FILED_VALUE in
        <foreach collection="batchNumbers" item="batchNumber" open="(" separator="," close=")">
            #{batchNumber}
        </foreach>
    </select>
</mapper>
