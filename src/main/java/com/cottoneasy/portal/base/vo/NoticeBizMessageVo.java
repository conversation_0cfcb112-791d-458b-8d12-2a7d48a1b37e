package com.cottoneasy.portal.base.vo;

import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NoticeBizMessageVo extends NoticeBizMessageEntity implements Serializable {
    private static final long serialVersionUID = 2837930204530197610L;

    private String customCode;

    private String customName;

    private String batchNumbers;

    private String bizType;

    private String operateTitle;

    private String url;
}
