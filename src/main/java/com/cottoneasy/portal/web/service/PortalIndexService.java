package com.cottoneasy.portal.web.service;

import com.sinosoft.dandelion.system.client.model.*;
import com.sinosoft.dandelion.system.client.model.theme.Theme;
import com.sinosoft.dandelion.system.client.service.CacheService;
import com.sinosoft.dandelion.system.client.service.PortalSystemService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.cms.base.entity.CmsSiteEntity;
import org.harry.dandelion.cms.base.entity.TemplateEntity;
import org.harry.dandelion.cms.base.service.SiteService;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.pac4j.core.http.ajax.AjaxRequestResolver;
import org.pac4j.core.http.ajax.DefaultAjaxRequestResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: PortalIndexService
 * @Description: 门户首页服务
 * <AUTHOR>
 * @date 2022年3月4日 上午11:34:47
 */
@Slf4j
@Service("portal.index.1")
@ApiRequestObject(value="应用门户首页服务",name="portalIndex",groups = {"SYSHOME-首页服务"},params = {
		@ApiParamMeta(key = "portalCode", desc = "门户编码", type = String.class)})
@ApiResponseObject(params= {
		@ApiParamMeta(key = "portal", desc = "门户信息",type = String.class),
		@ApiParamMeta(key = "site", desc = "站点信息",type = String.class),  
		@ApiParamMeta(key = "apps", desc = "应用菜单", multipart = true,type = String.class),  
		@ApiParamMeta(key = "indexMenu", desc = "默认首页",type = String.class),
		@ApiParamMeta(key = "permissions", desc = "权限列表",multipart = true, type = String.class),
		@ApiParamMeta(key = "loginUser", desc = "用户信息",type = String.class),
		@ApiParamMeta(key = "custom", desc = "客户信息",type = String.class),
		@ApiParamMeta(key = "enterprise", desc = "企业信息",type = String.class),
		@ApiParamMeta(key = "organ", desc = "组织信息",type = String.class),
		@ApiParamMeta(key = "theme", desc = "系统主题",type = String.class)
})
public class PortalIndexService implements IBusinessService {

	private static final String params_portalcode_key="portalCode";
	private static final String params_site_key="site";
	
	@Autowired(required = false)
	private PortalSystemService portalSystemService;
	
	@Autowired(required = false)
	private SiteService siteService;
	
	@Resource
	private CacheService cacheService;
	
	@Setter
	private AjaxRequestResolver ajaxRequestResolver = new DefaultAjaxRequestResolver();
	
	@Override
	public void doVerify(ServiceHandlerContext context) {
		if(portalSystemService==null) {
			log.error("门户服务不存在，无法获取门户信息");
		}
		String portalCode = context.getStringValue(params_portalcode_key);
		if(StringUtil.isEmpty(portalCode)) {
			CmsSiteEntity site = siteService.getSiteInfo(context.getHttpServletRequest());
			if(site==null) {
				throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "门户不存在");
			}
			context.bind(params_site_key, site);
			String portalId = site.getPortalId();
			if(StringUtil.isEmpty(portalId)) {
				throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "门户不存在");
			}
			Portal portal = portalSystemService.getPortal(portalId);
			if(portal==null) {
				throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "门户不存在");
			}
			portalCode = portal.getPortalCode();
		}
		if(StringUtil.isEmpty(portalCode)) {
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "门户不存在");
		}
		context.bind(params_portalcode_key, portalCode);
	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		String portalCode = (String) context.lookup(params_portalcode_key);
		Portal portal = portalSystemService.getPortalByCode(portalCode);
		CmsSiteEntity site = null;
		TemplateEntity indexTemplate=null;
		if( context.lookup(params_site_key)!=null) {
			site =(CmsSiteEntity) context.lookup(params_site_key);
		}else {
			site = siteService.getPortalBindSiteInfo(portal.getPortalId());
		}
		if(site==null) {
			throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "门户绑定站点不存在");
		}
		if(StringUtil.isNotEmpty(site.getIndexTemplateId())) {
			indexTemplate = siteService.getTemplateInfo(site.getIndexTemplateId());
		}
		
		Theme theme = cacheService.getCacheTheme(site.getThemeId());
		
		//当前账号基本信息
		LoginUser user = RuntimeContext.getRuntimeData(RuntimeContext.SYS_USER);
		Enterprise enterprise = RuntimeContext.getRuntimeData(RuntimeContext.SYS_COMPANY);
		Custom custom = RuntimeContext.getRuntimeData(RuntimeContext.SYS_CUSTOM);
		Organ organ  = RuntimeContext.getRuntimeData(RuntimeContext.SYS_ORG);
		
		//获取应用映射地址
		List<App> appDicts = new ArrayList<App>();
		List<App> apps = portalSystemService.getUserAvailabeApps(portalCode, user.getUserId());
		if(apps!=null && apps.size()>0) {
			appDicts =  portalSystemService.getPortalApps(null);
		}
		List<Menu> menus = portalSystemService.getPortalUserAvailabeMenus(portalCode, user.getUserId());
		Menu indexMenu = null;
		if(menus!=null&& menus.size()>0) {
			for(Menu menu:menus) {
				if(StringUtil.isNotEmpty(user.getIndexURL())&&user.getIndexURL().equals( menu.getMenuUrl())) {
					indexMenu = menu;
				}
				if(StringUtil.isEmpty(user.getIndexURL())&& indexTemplate!=null && menu.getMenuUrl().equals(indexTemplate.getComponentPath())) {
					indexMenu = menu;
				}
				for(App app:apps) {
					if(app.getAppId().equals(menu.getAppId())) {
						menu.setAppCode(app.getAppCode());
						menu.setAppName(app.getAppName());
						menu.setAppShortName(app.getAppShortName());
						menu.setAppVisitUrl(app.getVisitUrl());
						app.getMenus().add(menu);
					}
				}
			}
		}
		
		if(indexMenu==null && StringUtil.isNotEmpty(user.getIndexURL())) {
			TemplateEntity userIndexTemplate = siteService.getTemplateInfoByPath(user.getIndexURL());
			indexMenu = new Menu();
			if(userIndexTemplate!=null) {
				indexMenu.setComponent(userIndexTemplate.getComponentName());
				indexMenu.setMenuUrl(userIndexTemplate.getComponentPath());
			}else {
				indexMenu.setComponent(indexTemplate.getComponentName());
				indexMenu.setMenuUrl(indexTemplate.getComponentPath());
			}
		}
		
		//获取用户权限
		List<Permission> permissions = portalSystemService.findUserOperationPermissions(portalCode, user.getUserId());
		
		this.createSuccessResponse(context);
		
		context.bindResponseData("apps", apps);
		context.bindResponseData("site", site);
		context.bindResponseData("portal", portal);
		context.bindResponseData("appDicts", appDicts);
		context.bindResponseData("permissions", permissions);
		context.bindResponseData("loginUser", user);
		context.bindResponseData("custom", custom);
		context.bindResponseData("enterprise", enterprise);
		context.bindResponseData("organ", organ);
		context.bindResponseData("theme", theme);
		context.bindResponseData("indexMenu", indexMenu);
		
		//绑定用户权限
		if(permissions != null && permissions.size() > 0) {
		    List<String> permissionStrList = permissions.stream().map(Permission::getAccessCode).collect(Collectors.toList());
		    context.getResponseBody().setData("permissions", permissionStrList);
		}
	}
}
