package com.cottoneasy.portal.web.popup;

import com.sinosoft.dandelion.system.client.model.SysPopupMessage;
import com.sinosoft.dandelion.system.client.service.SysPopupMessageService;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 获取弹框消息
 * @date 2024/10/11 17:12
 */
@Slf4j
@Service("popup.queryPopupMessage.1")
@ApiRequestObject(value = "获取弹框消息", name = "queryPopupMessage", groups = {"SYSHOME-首页服务"}, params = {})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "popupMessageList", desc = "弹框消息", type = SysPopupMessage.class)
})
public class QueryMyMessageService implements IBusinessService {

    @Resource
    private SysPopupMessageService sysPopupMessageService;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        List<SysPopupMessage> popupMessageList = sysPopupMessageService.getPopupMessageList(context.getCurrentUserCustomCode());
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("popupMessageList", popupMessageList);
    }
}
