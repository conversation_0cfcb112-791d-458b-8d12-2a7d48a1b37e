package com.cottoneasy.portal.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.UserLoginAgreeRecord;
import com.cottoneasy.portal.base.entity.UserLoginAgreement;
import com.cottoneasy.portal.base.mappers.UserLoginAgreeRecordMapper;
import com.cottoneasy.portal.base.mappers.UserLoginAgreementMapper;
import com.sinosoft.dandelion.system.client.model.Custom;
import com.sinosoft.dandelion.system.client.model.Enterprise;
import com.sinosoft.dandelion.system.client.model.LoginUser;
import com.sinosoft.dandelion.system.client.model.Portal;
import com.sinosoft.dandelion.system.client.service.CacheService;
import com.sinosoft.dandelion.system.client.service.PortalSystemService;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.cms.base.entity.CmsSiteEntity;
import org.harry.dandelion.cms.base.service.SiteService;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.message.IMessageFactory;
import org.harry.dandelion.framework.core.message.response.ResponseMessage;
import org.harry.dandelion.framework.core.service.ServiceManager;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.security.common.DandelionSessionUtil;
import org.harry.dandelion.framework.security.config.SysLoginProperties;
import org.harry.dandelion.framework.security.pac4j.config.SecurityAutoConfig;
import org.harry.dandelion.framework.security.repository.BasicLoginVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Controller
@Slf4j
public class PortalLoginRedirectController {

    @Resource
	private UserLoginAgreementMapper loginAgreement;

	@Resource
	private UserLoginAgreeRecordMapper loginAgreeRecord;

	@Autowired(required = false)
	private PortalSystemService portalSystemService;
	
	@Autowired(required = false)
	private SiteService siteService;
	
	@Autowired
	private SysLoginProperties loginProperties;
	
	@Value("${login.indexURL:/}")
	private String defaultIndex;
	
	@Autowired
	private IMessageFactory messageFactory;
	@RequestMapping(value = "/redirect",method = {RequestMethod.GET,RequestMethod.POST})
	@ResponseBody
	public ResponseMessage loginForward(HttpServletRequest request,HttpServletResponse response) throws IOException {
		ResponseMessage message = messageFactory.createResponseMessage(Constants.succeedCode, "登录成功");
		String token = request.getParameter(SecurityAutoConfig.GET_PARAMS_TOKEN);
		String uid = RuntimeContext.getRuntimeData(RuntimeContext.SYS_USER_ID);
		String requestURL = request.getParameter(SecurityAutoConfig.REQUEST_URL);

		//更新登录协议
		updateUserLoginAgreement(uid);
		List<Portal> portals = portalSystemService.getUserPortals(uid);
		String redirect = null;
		
		/**
		 * 此处存在代码重复，待优化
		 * 门户校验规则
		 * 1、首先判断回调请求地址中是否有参数requestURL
		 * 2、requestURL是否符合当前用户访问门户权限
		 * 3、如果无当前门户访问权限，自动跳转到账号默认可访问门户，忽略request参数地址
		 * 4、如果当前用户可访问多个门户，则分别对门户请求地址进行判断。如果门户请求地址与其中某一个门户相同，则跳转到该门户地址
		 */
		if(StringUtil.isNotEmpty(requestURL)) { //回调服务地址判断
			String httpRequestURL = new String(Base64Utils.decodeFromUrlSafeString(requestURL));
			if(StringUtil.isNotEmpty(httpRequestURL) && (httpRequestURL.toLowerCase().startsWith("http://") || httpRequestURL.toLowerCase().startsWith("https://"))) {
				if(portals!=null && portals.size()==1) {
					CmsSiteEntity cms = siteService.getPortalBindSiteInfo(portals.get(0).getPortalId());
					if(httpRequestURL.toLowerCase().contains(cms.getExtranetUrl().toLowerCase())) {
						redirect = httpRequestURL;
					}else {
						log.warn("账号非法请求门户地址[{}]，默认返回门户首页[{}]",httpRequestURL,cms.getExtranetUrl());
						redirect = cms.getExtranetUrl();
					}
				}else if(portals!=null && portals.size()>1) { //现在是禁止一个账号绑定门户，此种情况不会发生，待后续扩展时，使用，此处需要优化门户访问地址多次访问数据库数据问题。可以添加缓存优化
					log.warn("当前登录账号{},可以访问多个门户{}",uid,portals);
					for(Portal portal:portals) {
						if(portal==null) continue;
						CmsSiteEntity cms = siteService.getPortalBindSiteInfo(portal.getPortalId());
						if(cms!=null && httpRequestURL.toLowerCase().contains(cms.getExtranetUrl().toLowerCase())) {
							redirect = httpRequestURL;
							break;
						}
					}
					//如果多个门户均没有requestURL地址，则返回到当前默认网站地址
					if(StringUtil.isEmpty(redirect)) {
						log.warn("账号非法请求门户地址[{}]，当前登录账号{},可以访问多个门户{},默认访问门户{}",httpRequestURL,uid,portals,portals.get(0));
						CmsSiteEntity cms = siteService.getPortalBindSiteInfo(portals.get(0).getPortalId());
						redirect = cms.getExtranetUrl();
					}
				}else if(portals==null || portals.size()<1) {
					log.warn("UID:[{}]账号未绑定任何门户,无法返回门户界面",uid);
					redirect="/";
				}
			}
			
		}
		if(StringUtil.isEmpty(redirect)) {
			if(portals!=null && portals.size()>0) {
				if(portals.size()>1) {
					log.warn("当前登录账号{},可以访问多个门户{},默认访问门户{}",uid,portals,portals.get(0));
				}
				for(Portal portal:portals) {
					if(portal==null) continue;
					CmsSiteEntity cms = siteService.getPortalBindSiteInfo(portal.getPortalId());
					if(cms!=null && StringUtil.isNotEmpty(cms.getExtranetUrl())) {
						redirect = cms.getExtranetUrl();
						break;
					}
				}
				
			}else {
				log.warn("UID:[{}]账号未绑定任何门户,无法返回门户界面",uid);
				redirect=defaultIndex;
			}
		}
		if(redirect!=null && !redirect.equalsIgnoreCase("/") && !redirect.equalsIgnoreCase(defaultIndex)) {
			if(redirect.contains("?")) {
				redirect+="&token="+token;
			}else {
				redirect+="?token="+token;
			}
		}
		
		if(loginProperties.getGlobalAuthFilter().equalsIgnoreCase(SysLoginProperties.cas_loginAuthFilter)) {
			response.sendRedirect(redirect);
		}else if(loginProperties.getGlobalAuthFilter().equalsIgnoreCase(SysLoginProperties.form_loginAuthFilter)){
			message.getBody().getDataSet().put("redirect", redirect);
		}else {
			log.warn("未知登录方式,默认重定向门户");
			response.sendRedirect(defaultIndex);
		}
		
		return message;
	}


	public void updateUserLoginAgreement(String userId) {
		if(StringUtil.isEmpty(userId)) {
			return;
		}
		LoginUser user = null;
		CacheService cahceService = ServiceManager.getBean(CacheService.class);
		if(cahceService!=null) {
			user = cahceService.getLoginUser(userId);
		}
		if(user==null) {
			user = (LoginUser) DandelionSessionUtil.getUser();
		}else {
			BasicLoginVerificationService userService = ServiceManager.getBean(BasicLoginVerificationService.class);
			user =(LoginUser) userService.findByUserId(userId);
		}
		if(user==null) return;
		Enterprise enterprise = user.getEnterprise();
		if (enterprise==null) return;
		Custom custom = user.getCustom();
		if (custom == null) return;
		//查询企业是否已经登录过
		//查询协议
		LambdaQueryWrapper<UserLoginAgreement> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(UserLoginAgreement::getDefaultAgreement,"0");
		UserLoginAgreement agreement = loginAgreement.selectOne(queryWrapper);
		//根据协议id和企业id查询记录表中是否有记录
		LambdaQueryWrapper<UserLoginAgreeRecord> query = Wrappers.lambdaQuery();
		query.eq(UserLoginAgreeRecord::getEnterpriseId,enterprise.getId());
		query.eq(UserLoginAgreeRecord::getAgreementId,agreement.getId());
		UserLoginAgreeRecord agreeRecord = loginAgreeRecord.selectOne(query);
		//如果协议记录表中没有数据则添加
		if (agreeRecord == null){
			UserLoginAgreeRecord record = new UserLoginAgreeRecord();
			record.setAgreementId(agreement.getId());
			record.setEnterpriseId(enterprise.getId());
			record.setCustomId(custom.getId());
			record.setCustomType(custom.getType());
			record.setUserId(userId);
			record.setLoginName(user.getLoginName());
			record.setCustomCode(custom.getCode());
			loginAgreeRecord.insert(record);
		}
	}
	
	public static void main(String[] args) {
		String httpRequestURL = new String(Base64Utils.decodeFromUrlSafeString("asdfdsf"));
		System.out.println("------------"+httpRequestURL);
	}
}
