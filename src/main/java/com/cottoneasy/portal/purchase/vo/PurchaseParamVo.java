package com.cottoneasy.portal.purchase.vo;

import lombok.Data;
import mybatis.mate.annotation.FieldEncrypt;

import java.util.Date;

@Data
public class PurchaseParamVo {


    private String Id;

    /**
     * 备注{json串}
     */
    private String remark;

    /**
     * 是否GC/绿卡棉
     */
    private String cardCotton;



    /**
     * 联系人
     */
    private String contact;



    /**
     * 联系电话/QQ/微信
     */
    @FieldEncrypt
    private String contactNum;


    /**
     * 企业名称
     */
    private String companyName;




    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最近更新时间
     */
    private Date updateTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人
     */
    private String updateUser;


    private PurchaseInfoVo purchaseInfoVo;



}
