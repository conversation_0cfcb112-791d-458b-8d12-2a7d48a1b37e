package com.cottoneasy.portal.purchase.service;

import com.alibaba.fastjson.JSONObject;
import com.cottoneasy.portal.base.mappers.CmsPurchaseMapper;
import com.cottoneasy.portal.purchase.vo.PurchaseInfoVo;
import com.cottoneasy.portal.purchase.vo.PurchaseParamVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("purchase.getPurchasePage.1")
@ApiRequestObject(value = "分页采购清单信息", name = "GetPurchasePage", groups = "cms-采购清单服务",
        params = {@ApiParamMeta(key = "purchase", desc = "投诉信息", type = PurchaseParamVo.class),

        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class GetPurchasePage implements IBusinessService {
    @Resource
    private CmsPurchaseMapper cmsPurchaseMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        PurchaseParamVo purchaseParamVo = context.getValueObject(PurchaseParamVo.class, "searchVo");
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        PipPagination<PurchaseParamVo> page = new PipPagination<>(pageParameter);
        page = cmsPurchaseMapper.selectPurchasePage(page, purchaseParamVo);
        List<PurchaseParamVo> result = page.getResult();
        for (PurchaseParamVo purchaseParam:result) {
            String remark = purchaseParam.getRemark();
            PurchaseInfoVo infoVo = JSONObject.parseObject(remark, PurchaseInfoVo.class);
            purchaseParam.setPurchaseInfoVo(infoVo);
        }
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", page);
    }

}
