package com.cottoneasy.portal.purchase.service;

import com.cottoneasy.portal.base.mappers.CmsPurchaseMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("purchase.DelPurchase.1")
@ApiRequestObject(value = "分页采购清单信息", name = "DelPurchase", groups = "cms-采购清单服务",
        params = {@ApiParamMeta(key = "ids", desc = "采购清单信息删除", type = String.class)})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class DelPurchase implements IBusinessService {
    @Resource
    private CmsPurchaseMapper cmsPurchaseMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        String ids = context.getStringValue("ids");
        if (StringUtil.isEmpty(ids)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "需求清单id不能为空！");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ids = context.getStringValue("ids");
        String[] split = ids.split(",");
        for (String id : split) {
            cmsPurchaseMapper.deleteById(id);
        }
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", true);
    }
}
