package com.cottoneasy.portal.purchase.service;

import com.cottoneasy.portal.base.entity.CmsPurchaseEntity;
import com.cottoneasy.portal.base.mappers.CmsPurchaseMapper;
import com.cottoneasy.portal.purchase.vo.PurchaseParamVo;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static cn.hutool.core.util.ObjectUtil.isNotEmpty;


@Service("purchase.addPurchase.1")
@ApiRequestObject(value = "新增采购清单信息", name = "AddPurchase", groups = "cms-采购清单服务",
        params = {@ApiParamMeta(key = "purchaseParamVo", desc = "采购清单", type = PurchaseParamVo.class),

        })
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", desc = "操作信息"),
})
public class AddPurchase implements IBusinessService {
    @Resource
    private CmsPurchaseMapper cmsPurchaseMapper;


    @Override
    public void doVerify(ServiceHandlerContext context) {
        PurchaseParamVo purchaseParamVo = context.getValueObject(PurchaseParamVo.class, "purchaseParamVo");
        if (isNotEmpty(purchaseParamVo)) {
            if (!isNotEmpty(purchaseParamVo.getRemark())) {
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "采购清单不能为空");
            }
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        CmsPurchaseEntity entity = new CmsPurchaseEntity();
        PurchaseParamVo purchaseParamVo = context.getValueObject(PurchaseParamVo.class, "purchaseParamVo");
        BeanUtils.copyProperties(purchaseParamVo, entity);
        cmsPurchaseMapper.insert(entity);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("message", "添加成功！");
    }
}
