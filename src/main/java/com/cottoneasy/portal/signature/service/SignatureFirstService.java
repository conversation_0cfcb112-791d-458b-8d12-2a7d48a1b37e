package com.cottoneasy.portal.signature.service;

import cn.hutool.core.io.FileUtil;
import com.cottoneasy.portal.signature.vo.SignatureFirstVo;
import org.harry.dandelion.cms.enums.signature.TemplatesEnum;
import org.harry.dandelion.framework.core.SystemBootstrap;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.document.actions.export.pdf.ExportPdfUtils;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.signature.SignatureType;
import org.harry.dandelion.framework.signature.extractors.SealImageExtractor;
import org.harry.dandelion.framework.signature.model.SignatureFirstResVo;
import org.harry.dandelion.framework.signature.model.strategy.Location;
import org.harry.dandelion.framework.signature.ra.RaService;
import org.harry.dandelion.framework.signature.service.SignatureService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;

@Service("signature.first.1")
@ApiRequestObject(value="签章",name="SignatureFirstService" , groups= {"签章"} ,params = {
        @ApiParamMeta(key = "publickey", desc = "公钥信息"),
        @ApiParamMeta(key = "ukeySerial", desc = "ukey序列号")
})
@ApiResponseObject(params= {
        @ApiParamMeta(key = "result" , type = SignatureFirstVo.class, desc = "第一步盖章结果"),
        @ApiParamMeta(key = "message" , desc = "请求结果")
})
public class SignatureFirstService implements IBusinessService {

    @Resource
    private SystemBootstrap systemBootstrap;

    @Resource
    private SealImageExtractor sealImageExtractor;

    @Resource
    private SignatureService signatureService;

    @Resource
    private RaService raService;


    @Override
    public void doVerify(ServiceHandlerContext context) {
        String ukeySerial = context.getStringValue("ukeySerial");
        String publickey = context.getStringValue("publickey");
        if(StringUtil.isBlank(publickey)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "缺少ukey的公钥证书！");
        }

        if(StringUtil.isBlank(ukeySerial)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "缺少ukey的序列号！");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ukeySerial = context.getStringValue("ukeySerial");
        String publickey = context.getStringValue("publickey");
        //生成PDF文件
        String number = String.valueOf(BizIdGenerator.getInstance().generateBizId());
        String templateName = TemplatesEnum.SIGNATURE.getTemplatesUrl();
        String destFilePath = systemBootstrap.getExportPath() + File.separator + number + ".pdf";
        ExportPdfUtils.getInstance().generateByFreemarkerTemplate(templateName, null, destFilePath);
        //获取PDF文件
        byte[] pdfresult = FileUtil.readBytes(destFilePath);

        //1、根据ukey序列号和客户代码，提取印章编码
        String customCode = RuntimeContext.getRuntimeData(RuntimeContext.SYS_CUSTOM_CODE);
        String dn = "";
        if (StringUtil.isNotBlank(ukeySerial) && ukeySerial.contains("_")) {
            String[] split = ukeySerial.split("_");
            ukeySerial = split[0];
            dn = split[1];
        }

        dn = dn.replaceAll(", ", ",");
        raService.validateRa(ukeySerial, dn);

        String sealImage = sealImageExtractor.querySealImage(ukeySerial , customCode);

        //设置关键字类型
        Location location = new Location();
        location.setKeyword("*0-0-1");
        location.setSignatureType(SignatureType.KEYWORD);

        SignatureFirstResVo vo;
        try {
            vo = signatureService.sealFirstRequest(null, pdfresult, publickey.getBytes(), location, sealImage,180, null);
        } catch (Exception e) {
            log.error("第一次签章请求失败！",e);
            throw new BusinessException(e);
        }

        SignatureFirstVo signatureFirstVo = new SignatureFirstVo();
        BeanUtils.copyProperties(vo,signatureFirstVo);
        signatureFirstVo.setNumber(number);

        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", signatureFirstVo);
    }
}
