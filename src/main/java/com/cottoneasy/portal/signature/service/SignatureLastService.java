package com.cottoneasy.portal.signature.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.cottoneasy.portal.signature.vo.SignatureLastVo;
import com.sinosoft.dandelion.fms.base.FileInfoVo;
import com.sinosoft.dandelion.fms.proxy.FileMetaProxy;
import com.sinosoft.dandelion.fms.proxy.FileProxy;
import org.harry.dandelion.cms.resource.common.vo.FileVo;
import org.harry.dandelion.framework.common.utils.FileUtils;
import org.harry.dandelion.framework.common.utils.ListUtils;
import org.harry.dandelion.framework.core.SystemBootstrap;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.signature.SignatureEngineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("signature.last.1")
@ApiRequestObject(value="签章2",name="SignatureLastService",groups= {"签章"},params= {
        @ApiParamMeta(key = "signature", desc = "签章第二次请求参数", type = SignatureLastVo.class),
})
@ApiResponseObject(params= {
        @ApiParamMeta(key = "path" , type = String.class, desc = "文件路径"),
})
public class SignatureLastService implements IBusinessService {

    //签章服务
    @Resource
    private SignatureEngineService signatureEngineService;

    @Resource
    private SystemBootstrap systemBootstrap;

    @Resource
    private FileProxy fileProxy;

    @Resource
    private FileMetaProxy fileMetaProxy;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        SignatureLastVo signature = context.getValueObject(SignatureLastVo.class, "signature");

        //发送签章合成请求
        byte[] pdf = signatureEngineService.sealLastRequest(signature.getSeriaNumber(), signature.getPdfSignature(), signature.getPdfId());
        String destFilePath = systemBootstrap.getExportPath() + File.separator + signature.getNumber() + ".pdf";
        try {
            FileUtils.createFile(pdf, systemBootstrap.getExportPath(), signature.getNumber() + ".pdf");
        } catch (IOException e) {
            log.error("文件盖章失败，失败原因：{}" , e.getMessage(), e);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE , e.getMessage());
        }

        //定义文件存储服务端的目录
        String folder2 = "signature" + File.separator + DateUtil.year(new Date()) +  File.separator + DateUtil.month(new Date()) +1;
        //上传签章文件到文件存储
        Long startTime1 = System.currentTimeMillis();
        Long uploadPdfId = this.upload(FileUtil.file(destFilePath), FileUtil.getName(FileUtil.file(destFilePath)), folder2);
        Long endTime1 = System.currentTimeMillis();
        log.info("上传签章文件耗时{}", endTime1 - startTime1);
        List<FileVo> fileVos = this.getFileVos(String.valueOf(uploadPdfId));
        String url = fileVos.get(0).getPreviewUrl();


        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("path", url);
    }


    public Long upload(File file,String FileName,String folder) {
        Object uploadId = null;
        FileInputStream fileInputStream;
        if (StringUtil.isNotEmpty(file)) {
            try {
                fileInputStream = new FileInputStream(file);
                uploadId = fileProxy.upload(fileInputStream, FileName, folder);
            } catch (FileNotFoundException e) {
                log.error("文件接收失败，失败原因：" + e.getMessage());
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "文件接收失败！");
            }
        }
        return Long.parseLong(uploadId.toString());
    }


    public List<FileVo> getFileVos(String repFileId){
        List<FileVo> list = new ArrayList<>();
        List<FileInfoVo> fileInfoMetas = fileMetaProxy.getFileInfoMetas(repFileId);
        if (!ListUtils.isNullOrEmpty(fileInfoMetas)){
            try {
                list = ListUtils.copyTo(fileInfoMetas, FileVo.class);
            } catch (Exception e) {
                log.error("数据转换异常，失败原因：" + e.getMessage());
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE , "数据转换异常！");
            }
        }
        return list;
    }
}
