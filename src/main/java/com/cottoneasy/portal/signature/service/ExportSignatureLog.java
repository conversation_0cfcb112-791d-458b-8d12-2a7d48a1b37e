package com.cottoneasy.portal.signature.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.cottoneasy.portal.signature.vo.StgSignatureLogVo;
import com.cottoneasy.uc.api.moulage.Moulage4CustomRelService;
import com.cottoneasy.uc.base.bean.StgSignatureLogInfo;
import lombok.SneakyThrows;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @Title: ExportTwoContractInfo.java
 * @Description: 导出盖章记录信息
 * @version V1.0
 */
@Service("signature.exportSignatureLog.1")
@ApiRequestObject(value = "导出盖章记录信息", name = "exportTwoContractInfo", groups = {"签章"}, params = {
        @ApiParamMeta(key = "stgSignatureLogVo", desc = "查询参数", type = StgSignatureLogVo.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息")
})
public class ExportSignatureLog implements IBusinessService {

    @Resource
    private Moulage4CustomRelService moulage4CustomRelService;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @SneakyThrows
    @Override
    public void doWork(ServiceHandlerContext context) {
        HttpServletResponse response = context.getHttpServletResponse();
        //获取参数
        StgSignatureLogVo stgSignatureLogVo = context.getValueObject(StgSignatureLogVo.class, "stgSignatureLogVo");
        List<StgSignatureLogInfo> stgSignatureLogInfoList = moulage4CustomRelService.getStgSignatureLogInfoList(stgSignatureLogVo.getCustomName(), stgSignatureLogVo.getCustomCode(), stgSignatureLogVo.getUserRealName(), stgSignatureLogVo.getMoulageRemark(), null, stgSignatureLogVo.getSignBusinessType(), stgSignatureLogVo.getStampedType(), stgSignatureLogVo.getUkeySerial(), stgSignatureLogVo.getSignatureTimeStart(), stgSignatureLogVo.getSignatureTimeEnd());
        List<StgSignatureLogVo> stgSignatureLogVos = new ArrayList<>();
        for (StgSignatureLogInfo stgSignatureLogInfo : stgSignatureLogInfoList) {
            StgSignatureLogVo stgSignatureLogVo1 = new StgSignatureLogVo();
            BeanUtils.copyProperties(stgSignatureLogInfo,stgSignatureLogVo1);
            stgSignatureLogVos.add(stgSignatureLogVo1);
        }
        ExcelWriter writer = null;
        try {
            String fileName = "盖章记录信息.xlsx";
            String downloadFileName = URLEncoder.encode(fileName,"UTF-8");
            // 设置响应类型为html，编码为utf-8，处理相应页面文本显示的乱码
            response.setContentType("application/octet-stream");
            // 设置文件头：最后一个参数是设置下载文件名
            response.setHeader("content-type", "application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=" + downloadFileName);
            //生成文件
        	log.debug("==============导出开始==============");
        	ServletOutputStream out = response.getOutputStream();
            writer = EasyExcel.write(out).build();
            // .writerSheet里面放入两个参数 1. sheet编号（从0开始）  2. sheet名字
            // .head里面放入 实体类的class
            WriteSheet sheet = EasyExcel.writerSheet(0, "sheet0")
                    .head(StgSignatureLogVo.class)
                    .build();
            //写入
            writer.write(stgSignatureLogVos,sheet);
            log.debug("==============导出完成==============");
        } catch (IOException e) {
            log.error("发生异常 异常原因:{} {}", e.getMessage(), e);
        } catch (Exception j) {
            log.error("发生异常 异常原因:{} {}", j.getMessage(), j);
        } finally {
            // 关闭流
            if (writer != null) {
                writer.finish();
            }
        }
        log.debug("==============导出完成==============");

        //构造响应参数
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("success", true);
        context.getResponseBody().getDataSet().put("message", "修改成功！");
    }
}
