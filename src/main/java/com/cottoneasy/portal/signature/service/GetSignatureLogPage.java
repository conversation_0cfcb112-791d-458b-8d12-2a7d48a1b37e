package com.cottoneasy.portal.signature.service;

import com.cottoneasy.portal.signature.vo.StgSignatureLogVo;
import com.cottoneasy.uc.api.moulage.Moulage4CustomRelService;
import com.cottoneasy.uc.base.bean.StgSignatureLogInfo;
import com.cottoneasy.uc.base.entity.StgSignatureLogEntity;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 
 * @Title: GetSignatureLogPage.java
 * @Description: 获取盖章记录
 * <AUTHOR>
 * @date 2023年8月28日
 * @version V1.0
 */
@Service("signature.getSignatureLogPage.1")
@ApiRequestObject(value = "获取盖章记录", name = "getSignatureLogPage", groups = {"签章"}, params = {
        @ApiParamMeta(key = "stgSignatureLogVo", desc = "查询参数", type = StgSignatureLogVo.class),
		@ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "result",type = StgSignatureLogEntity.class,desc = "返回结果"),
})
public class GetSignatureLogPage implements IBusinessService {

	@Resource
	private Moulage4CustomRelService moulage4CustomRelService;

	@Override
    public void doVerify(ServiceHandlerContext context) {
		PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
		if (pageParameter == null){
			throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "分页参数不能为空！");
		}
	}

	@Override
    public void doWork(ServiceHandlerContext context) {
		StgSignatureLogVo stgSignatureLogVo = context.getValueObject(StgSignatureLogVo.class, "stgSignatureLogVo");
		PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
		Pagination<StgSignatureLogInfo> pagination = moulage4CustomRelService.getStgSignatureLogInfoPageList(stgSignatureLogVo.getCustomName(), stgSignatureLogVo.getCustomCode(), stgSignatureLogVo.getUserRealName(), stgSignatureLogVo.getMoulageRemark(), null, stgSignatureLogVo.getSignBusinessType(),stgSignatureLogVo.getStampedType(), stgSignatureLogVo.getUkeySerial(), stgSignatureLogVo.getSignatureTimeStart(), stgSignatureLogVo.getSignatureTimeEnd(), pageParameter);
		Pagination<StgSignatureLogVo> stgSignatureLogVoPagination = new Pagination<>();
		//文件ID反序列化后不对,转为中文 按照时间倒叙
		if (pagination != null && CollectionUtils.isNotEmpty(pagination.getResult())){
			 this.buildPagination(pagination,stgSignatureLogVoPagination);
		}
		this.createSuccessResponse(context);
		context.getResponseBody().setData("result", stgSignatureLogVoPagination);
    }
    
    private Pagination<StgSignatureLogVo> buildPagination(Pagination<StgSignatureLogInfo> pagination,Pagination<StgSignatureLogVo> stgSignatureLogVoPagination){
		BeanUtils.copyProperties(pagination,stgSignatureLogVoPagination);
		List<StgSignatureLogInfo> result = pagination.getResult();
		List<StgSignatureLogVo> stgSignatureLogVos = new ArrayList<>();
		for (StgSignatureLogInfo stgSignatureLogInfo : result) {
			StgSignatureLogVo stgSignatureLogVo = new StgSignatureLogVo();
			BeanUtils.copyProperties(stgSignatureLogInfo,stgSignatureLogVo);
			if (stgSignatureLogInfo.getSignatureFileId() != null){
				stgSignatureLogVo.setSignatureFileId(stgSignatureLogInfo.getSignatureFileId().toString());
			}
			stgSignatureLogVos.add(stgSignatureLogVo);
		}
		stgSignatureLogVoPagination.setResult(stgSignatureLogVos);
		return stgSignatureLogVoPagination;
	}
}
