package com.cottoneasy.portal.signature.service;

import cfca.sadk.util.Base64;
import com.sun.xml.internal.messaging.saaj.util.ByteOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.BASE64CodeUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.signature.extractors.SealImageExtractor;
import org.harry.dandelion.framework.signature.ra.RaService;
import org.harry.dandelion.framework.signature.service.SignatureService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Service("offline.signature.1")
@ApiRequestObject(value = "线下签章", name = "OfflineSignature", groups = {"签章"}, params = {
        @ApiParamMeta(key = "ukeySerial", desc = "ukey序列号", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "message", type = String.class, desc = "请求结果"),
        @ApiParamMeta(key = "result", type = String.class, desc = "印模")
})
@Slf4j
public class OfflineSignature implements IBusinessService {

    @Resource
    private SignatureService signatureService;

    @Resource
    private SealImageExtractor sealImageExtractor;

    @Resource
    private RaService raService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        String ukeySerial = context.getValueObject(String.class, "ukeySerial");
        if (StringUtil.isBlank(ukeySerial)) {
            log.warn("线下盖章, 参数为空: ukeySerial: {}", ukeySerial);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "签章参数不能为空！");
        }

        String[] split = ukeySerial.split("_");
        if (split.length != 2) {
            log.warn("线下盖章, 参数格式异常: ukeySerial: {}", ukeySerial);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "签章参数格式异常！");
        }

        raService.validateRa(split[0], split[1]);

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ukeySerial = context.getValueObject(String.class, "ukeySerial");
        String customCode = RuntimeContext.getRuntimeData(RuntimeContext.SYS_CUSTOM_CODE);
        String[] split = ukeySerial.split("_");
        ukeySerial = split[0];
        String sealImage = sealImageExtractor.querySealImage(ukeySerial, customCode);
        if (StringUtil.isBlank(sealImage)) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "获取印模失败！");
        }
        List<String> unScaleCode = Arrays.asList("ym2023062913900192", "ym2023021456498704");
        String sealImage1;
        try {
            if (unScaleCode.contains(sealImage)) {
                // 方形章，不变换比例
                sealImage1 = signatureService.getSealImage(sealImage);
                sealImage1 = this.zoomImg(sealImage1, BigDecimal.valueOf(0.46));
            } else {
                sealImage1 = signatureService.getSealImage(sealImage, 120, 120);
            }
        } catch (Exception e) {
            log.error("获取印模失败！");
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "获取印模失败！");
        }

        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("result", sealImage1);
    }

    /**
     * 缩放图片
     *
     * @param sourceImage 源图片文件
     * @param scale       比例
     */
    private String zoomImg(String sourceImage, BigDecimal scale) throws IOException {
        try (ByteArrayInputStream imageInputStream = new ByteArrayInputStream(Base64.decode(sourceImage))) {
            BufferedImage bufferedImage = ImageIO.read(imageInputStream);
            int newWidth = (int) (bufferedImage.getWidth() * scale.doubleValue());
            int newHeight = (int) (bufferedImage.getHeight() * scale.doubleValue());
            BufferedImage bufferedImageScaled = new BufferedImage(newWidth, newHeight, bufferedImage.getType());

            Graphics g = bufferedImageScaled.getGraphics();
            try {
                g.drawImage(bufferedImage.getScaledInstance(newWidth, newHeight, 4), 0, 0, null);
            } finally {
                g.dispose(); // 释放图形上下文资源
            }

            return BASE64CodeUtils.encode(this.convertBufferedImageToBytes(bufferedImageScaled));
        }
    }

    private byte[] convertBufferedImageToBytes(BufferedImage image) {
        byte[] result = null;

        try (ByteOutputStream byteOutputStream = new ByteOutputStream()) {
            ImageIO.write(image, "png", byteOutputStream);
            result = byteOutputStream.getBytes();
        } catch (IOException e) {
            log.warn("印模缩放失败", e);
        }
        return result;
    }
}
