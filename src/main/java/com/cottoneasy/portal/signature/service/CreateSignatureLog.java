package com.cottoneasy.portal.signature.service;

import com.cottoneasy.uc.api.moulage.Moulage4CustomRelService;
import com.sinosoft.dandelion.fms.proxy.FileProxy;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.CollectionUtils;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.web.common.UploadFile;
import org.harry.dandelion.framework.web.message.file.UploadFileMessageBody;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


/**
 * 
 * @Title: CreateProxyContract.java
 * @Description: 创建盖章记录
 * <AUTHOR>
 * @date 2023年8月28日
 * @version V1.0
 */
@Service("signature.createSignatureLog.1")
@ApiRequestObject(value = "创建盖章记录", name = "createSignatureLog", groups = {"签章"}, params = {
        @ApiParamMeta(key = "files", desc = "文件", type = MultipartFile.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success",desc = "返回结果",type = boolean.class),
        @ApiParamMeta(key = "message",desc = "返回消息"),
})
public class CreateSignatureLog implements IBusinessService {

	@Resource
	private FileProxy fileProxy;
	
	@Resource
	private Moulage4CustomRelService moulage4CustomRelService;

	@Override
    public void doVerify(ServiceHandlerContext context) {
		String ukeySerial = context.getRequestBody().getDataSet().get("ukeySerial").toString();
		//验证数据是否为空
		if (ukeySerial == null){
			throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "参数不能为空！");
		}
	}

    @SuppressWarnings("unchecked")
	@Override
    public void doWork(ServiceHandlerContext context) {
		//接收文件信息
		List<UploadFile> files = new ArrayList<>();
		if (context.getRequestBody().getDataSet().get("files") != null){
			UploadFileMessageBody body = (UploadFileMessageBody) context.getRequestBody();
			files = (List<UploadFile>) body.getDataSet().get("files");
		}
		//接收参数
		String ukeySerial = context.getRequestBody().getDataSet().get("ukeySerial").toString();
		String signatureBusinessType = context.getRequestBody().getDataSet().get("signatureBusinessType").toString();
		String stampType = context.getRequestBody().getDataSet().get("stampType").toString();
		//创建盖章记录
		try {
			this.createSignatureLog(files,ukeySerial,signatureBusinessType,stampType,context);
		} catch (IOException e) {
			throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, e.getMessage());
		}
		this.createSuccessResponse(context);
		context.getResponseBody().setData("success", true);
		context.getResponseBody().setData("message", "添加成功");
    }

	/**
	 * 构造盖章记录数据
	 * @param files
	 * @param ukeySerial
	 * @param context
	 * @throws IOException
	 */
	private void createSignatureLog(List<UploadFile> files, String ukeySerial,String signatureBusinessType,String stampType, ServiceHandlerContext context) throws IOException {
		//交易商代码
		String customCode = context.getCurrentUserCustomCode();
		//实际登录人名称
		String currentUserRealName = context.getCurrentUserRealName();
		
		log.debug("构造盖章记录数据");
		if (CollectionUtils.isNotEmpty(files)){
			for (int i = 0; i < files.size(); i++) {
				UploadFile file = files.get(i);
				InputStream inputStream=file.getInputStream();
				String fileName = file.getOriginalFilename();
				String folderName = DateUtils.getYear() + "盖章记录" + File.separator + DateUtils.getTime()+i;
				Long fileId = this.uploadFile(inputStream, fileName, folderName);
				//添加合同附件数据
				moulage4CustomRelService.addStgSignatureLog(customCode,currentUserRealName,ukeySerial,fileId.toString(),signatureBusinessType,stampType,DateUtils.getTime(),null);
			}
		}else {
			//添加合同附件数据
			moulage4CustomRelService.addStgSignatureLog(customCode,currentUserRealName,ukeySerial,null,signatureBusinessType,stampType,DateUtils.getTime(),null);
		}
	}

	/**
	 * 上传文件
	 * @param inputStream
	 * @param fileName
	 * @param folderName
	 * @return
	 */
	private Long uploadFile(InputStream inputStream,String fileName,String folderName) {
		Long uploadFileId  = null;
		if(StringUtil.isNotEmpty(inputStream)) {
			uploadFileId = fileProxy.upload(inputStream,fileName, folderName);
		}
		return uploadFileId;
	}
}
