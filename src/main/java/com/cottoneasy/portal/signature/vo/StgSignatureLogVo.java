package com.cottoneasy.portal.signature.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cottoneasy.portal.base.converter.CustomDataItemConverter;
import com.cottoneasy.portal.base.converter.CustomLongConverter;
import com.cottoneasy.portal.base.converter.DataTimeConverter;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
public class StgSignatureLogVo implements Serializable {

	private static final long serialVersionUID = 1189738806763177574L;

	/**
	 * 主键ID
	 */
	@ExcelIgnore
	@TableId(value = "ID")
	@JSONField(deserializeUsing = ToStringSerializer.class)
	private Long id;

	/**
	 * 客户代码
	 */
	@ExcelProperty(value = "客户代码", index = 0)
	@TableField(value = "CUSTOM_CODE")
	private String customCode;

	/**
	 * 客户名称
	 */
	@ExcelProperty(value = "客户名称", index = 1)
	@TableField(value = "CUSTOM_NAME")
	private String customName;

	/**
	 * 用户真实名称
	 */
	@ExcelProperty(value = "用户名称", index = 2)
	@TableField(value = "USER_REAL_NAME")
	private String userRealName;

	/**
	 * 签章印模说明
	 */
	@ExcelProperty(value = "签章印模说明", index = 3)
	@TableField(value = "MOULAGE_REMARK")
	private String moulageRemark;

	/**
	 * 盖章设备ID
	 */
	@ExcelProperty(value = "盖章设备ID", index = 4)
	@TableField(value = "UKEY_SERIAL")
	private String ukeySerial;

	/**
	 * 签章业务类型(1、线下盖章2、运输补贴)
	 */
	@ExcelProperty(value = "盖章业务类型", index = 5,converter = CustomDataItemConverter.class)
	@TableField(value = "SIGNATURE_BUSINESS_TYPE")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem signatureBusinessType;

	/**
	 * 盖章类型(1、圆形章2、骑缝章)
	 */
	@ExcelProperty(value = "盖章类型", index = 6,converter = CustomDataItemConverter.class)
	@TableField(value = "STAMP_TYPE")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem stampType;

	/**
	 * 盖章时间
	 */
	@ExcelProperty(value = "盖章时间", index = 7,converter = DataTimeConverter.class)
	@TableField(value = "SIGNATURE_TIME")
	private Date signatureTime;

	/**
	 * 盖章文件ID
	 */
	@ExcelProperty(value = "盖章文件ID", index = 8,converter = CustomLongConverter.class)
	@TableField(value = "SIGNATURE_FILE_ID")
	@JSONField(deserializeUsing = ToStringSerializer.class)
	private String signatureFileId;

	/**
	 * 查询使用
	 */
	@ExcelIgnore
	private String signBusinessType;

	@ExcelIgnore
	private String stampedType;

	@ExcelIgnore
	private String signatureTimeStart;

	@ExcelIgnore
	private String signatureTimeEnd;
}
