package com.cottoneasy.portal.todo.init;

import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.entity.PopupConfigEntity;
import com.cottoneasy.portal.base.service.NoticeBizService;
import com.cottoneasy.portal.base.service.PopupConfigService;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.cache.ICache;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

import static com.cottoneasy.portal.todo.TodoConstants.REGION_BIZ_CONFIG;
import static com.cottoneasy.portal.todo.TodoConstants.REGION_POPUP_CONFIG;

/**
 * 待办的初始化配置
 * 构建未归档待办空间、客户待办空间、客户已处理待办空间、业务配置空间
 */
@Component
@Slf4j
public class TodoConfigInitService {

    @Resource(name = REGION_POPUP_CONFIG)
    private ICache configCache;

    @Resource(name = REGION_BIZ_CONFIG)
    @Lazy
    private ICache bizConfigCache;

    @Resource
    private PopupConfigService popupConfigService;

    @Resource
    private NoticeBizService noticeBizService;

    /**
     * 初始化配置信息
     */
    @PostConstruct
    public void init(){
        /// 初始化待办提醒的配置信息
        this.initConfig();
        /// 初始化业务通知配置
        this.initBizConfig();
        /// 初始化业务通知列表配置
    }

    private void initConfig(){
        if(configCache == null){
            log.error("待办缓存[全局配置区域]未初始化，请检查配置！！！！");
        }
        List<PopupConfigEntity> popupConfigList = this.popupConfigService.getValidConfig();
        if(popupConfigList != null){
            for(PopupConfigEntity popupConfig : popupConfigList) {
                String key = popupConfig.getConfigId().toString();
                configCache.put(key, popupConfig);
            }
        }
    }

    private void initBizConfig(){
        if(bizConfigCache == null){
            log.error("待办缓存[业务配置区域]未初始化，请检查配置！！！！");
        }
        List<NoticeBizEntity> bizConfigList = this.noticeBizService.getAllConfig();
        if(bizConfigList != null && !bizConfigList.isEmpty()){
            for(NoticeBizEntity bizConfig : bizConfigList) {
                String key = bizConfig.getNoticeBizCode();
                configCache.put(key, bizConfig);
            }
        }
    }
}
