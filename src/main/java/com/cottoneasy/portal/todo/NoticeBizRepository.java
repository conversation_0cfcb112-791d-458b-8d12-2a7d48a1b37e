package com.cottoneasy.portal.todo;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.base.entity.NoticeContentEntity;
import com.cottoneasy.portal.base.entity.NoticeObjectEntity;
import com.cottoneasy.portal.base.enums.NoticeHandleStatusEnum;
import com.cottoneasy.portal.base.service.NoticeBizMessageService;
import com.cottoneasy.portal.base.service.NoticeContentService;
import com.cottoneasy.portal.base.service.NoticeObjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class NoticeBizRepository {

    private final NoticeBizMessageService noticeBizMessageService;

    private final NoticeObjectService noticeObjectService;

    private final NoticeContentService noticeContentService;


    public void saveBatchNoticeBizMessage(Collection<NoticeBizMessageEntity> noticeBizMessageEntities) {
        noticeBizMessageService.saveBatch(noticeBizMessageEntities);
    }

    public void saveBatchNoticeObject(Collection<NoticeObjectEntity> noticeObjectEntities) {
        noticeObjectService.saveBatch(noticeObjectEntities);
    }

    public void saveBatchNoticeContent(Collection<NoticeContentEntity> noticeContentEntities) {
        noticeContentService.saveBatch(noticeContentEntities);
    }

    /**
     * 更新待办处理状态
     *
     * @param noticeObjectCode 通知对象编码
     * @param bizIds           业务id
     */
    public void updateTodoDisposeStatus(String noticeObjectCode, Collection<String> bizIds) {
        this.updateTodoDisposeStatus(noticeObjectCode, bizIds, null, null);
    }

    /**
     * 更新待办处理状态
     *
     * @param noticeObjectCode 通知对象编码
     * @param bizIds           业务id
     * @param createUserId     createUserId 创建人id
     * @param createUserName   createUserName 创建人名称
     */
    public void updateTodoDisposeStatus(String noticeObjectCode, Collection<String> bizIds, String createUserId, String createUserName) {
        /*
            业务通知主表 DE_SYS_NOTICE_BIZ_MESSAGE;
            通知对象表 DE_SYS_NOTICE_OBJECT
            一次业务通知可能会通知多个对象, 所以他们是 1:N
            先更新通知对象表, 如果所有的通知对象都处理了，那么更新业务通知主表为已处理
         */
        if (Objects.isNull(bizIds) || bizIds.isEmpty()) {
            return;
        }

        // 更新通知对象表
        LambdaUpdateWrapper<NoticeObjectEntity> noticeObjectUpdate = Wrappers.lambdaUpdate();
        noticeObjectUpdate.eq(NoticeObjectEntity::getNoticeObjectCode, noticeObjectCode);
        noticeObjectUpdate.in(NoticeObjectEntity::getBizMessageId, bizIds);
        noticeObjectUpdate.set(NoticeObjectEntity::getNoticeHandleStatus, NoticeHandleStatusEnum.HANDLED.getValue());
        noticeObjectUpdate.set(NoticeObjectEntity::getUpdateTime, LocalDateTime.now());
        noticeObjectUpdate.set(NoticeObjectEntity::getUpdateUserId, createUserId);
        noticeObjectUpdate.set(NoticeObjectEntity::getUpdateUserName, createUserName);
        // 能触发自动更新的方法
        noticeObjectService.update(new NoticeObjectEntity(), noticeObjectUpdate);

        // 更新业务通知主表, 使用 EXISTS 子查询, 只更新所有关联的通知对象都处理了的业务通知主表
        LambdaUpdateWrapper<NoticeBizMessageEntity> noticeBizMessageUpdate = Wrappers.lambdaUpdate();
        noticeBizMessageUpdate
                .in(NoticeBizMessageEntity::getId, bizIds) // 只在本次操作相关的bizIds中查找
                .eq(NoticeBizMessageEntity::getDisposeStatus, NoticeHandleStatusEnum.UN_HANDLE.getValue()) // 避免重复更新
                .set(NoticeBizMessageEntity::getDisposeTime, LocalDateTime.now())
                .set(NoticeBizMessageEntity::getDisposeStatus, NoticeHandleStatusEnum.HANDLED.getValue())
                // 关键：使用 EXISTS 子查询作为更新条件
                .apply("NOT EXISTS (SELECT 1 FROM de_sys_notice_object o " +
                        "WHERE o.biz_message_id = de_sys_notice_biz_message.id " +
                        "AND o.notice_handle_status = {0})", NoticeHandleStatusEnum.UN_HANDLE.getValue());
        // 能触发自动更新的方法
        noticeBizMessageService.update(new NoticeBizMessageEntity(), noticeBizMessageUpdate);
    }
}
