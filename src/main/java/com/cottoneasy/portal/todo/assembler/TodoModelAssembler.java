package com.cottoneasy.portal.todo.assembler;

import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.base.entity.NoticeObjectEntity;
import com.cottoneasy.portal.base.enums.DelFlagEnum;
import com.cottoneasy.portal.base.enums.NoticeHandleStatusEnum;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;

/**
 * 待办模型工厂
 */
public class TodoModelAssembler {

    private TodoModelAssembler() {
    }

    /**
     * 从todoModel转换为NoticeBizMessageEntity
     *
     * @param view    待办模型
     * @param bizMessageId 业务通知主表id
     * @param bizConfigId  业务配置id
     * @return NoticeBizMessageEntity
     */
    public static NoticeBizMessageEntity getNoticeBizMessageFromTodoModel(BatchNumberUrlArchiveCacheableView view,
                                                                          Long bizMessageId, Long bizConfigId) {
        NoticeBizMessageEntity noticeBizMessageEntity = new NoticeBizMessageEntity();
        noticeBizMessageEntity.setId(bizMessageId);
        noticeBizMessageEntity.setBizConfigId(bizConfigId);
        noticeBizMessageEntity.setAppCode(view.getAppCode());
        noticeBizMessageEntity.setBusinessId(view.getBizId());
        noticeBizMessageEntity.setDisposeStatus(NoticeHandleStatusEnum.UN_HANDLE.getValue());
        noticeBizMessageEntity.setDelFlag(DelFlagEnum.NOT_DELETED.getValue());
        noticeBizMessageEntity.setCreateTime(view.getCreateTime());
        noticeBizMessageEntity.setCreateUserName(view.getCreateUserName());
        noticeBizMessageEntity.setCreateUserId(view.getCreateUserId());
        return noticeBizMessageEntity;
    }

    /**
     * 从todoModel转换为NoticeObjectEntity
     *
     * @param view      待办模型
     * @param bizMessageId   业务通知主表id
     * @param noticeObjectId 通知对象id
     * @return NoticeObjectEntity
     */
    public static NoticeObjectEntity getNoticeObjectFromTodoModel(BatchNumberUrlArchiveCacheableView view, Long bizMessageId,
                                                                  Long noticeObjectId) {
        NoticeObjectEntity noticeObjectEntity = new NoticeObjectEntity();
        noticeObjectEntity.setId(noticeObjectId);
        noticeObjectEntity.setBizMessageId(bizMessageId);
        noticeObjectEntity.setNoticeObjectCode(view.getTargetObjectCode());
        noticeObjectEntity.setNoticeObjectName(view.getTargetObjectName());
        noticeObjectEntity.setNoticeObjectType(view.getNoticeObjectType().getValue());
        noticeObjectEntity.setNoticeHandleStatus(NoticeHandleStatusEnum.UN_HANDLE.getValue());
        noticeObjectEntity.setDelFlag(DelFlagEnum.NOT_DELETED.getValue());
        return noticeObjectEntity;
    }
}
