package com.cottoneasy.portal.todo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NoticeBizVO {

    private Long noticeConfigId;

    @ApiModelProperty(value = "业务通知编码，自定义，全局唯一")
    private String noticeBizCode;

    @ApiModelProperty(value = "图标")
    private String noticeIcon;

    @ApiModelProperty(value = "标题")
    private String noticeTitle;

    @ApiModelProperty(value = "优先级")
    private Integer noticePriority;

    @ApiModelProperty(value = "通知处理形式 1、阅读形式 2、业务形式")
    private Integer noticeHandleType;

    @ApiModelProperty(value = "唤醒方式：0、不唤醒(仅在登录提醒)，1-定时提醒，2、特定路径请求唤醒，3、全局任意请求提醒")
    private Integer remindType;

    @ApiModelProperty(value = "定时时间（分钟）")
    private Integer remindMinutes;
}
