package com.cottoneasy.portal.todo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PopupConfigVO {

    @ApiModelProperty(value = "是否可关闭：0是，1否")
    private Integer closeStatus;

    @ApiModelProperty(value = "通知框位置,如左上、左下、居中、右上、右下")
    private String position;

    @ApiModelProperty(value = "是否自动消失 0-是，1-否")
    private Integer autoClose;

    @ApiModelProperty(value = "持续时间后消失(秒)")
    private Integer holdTime;

}
