package com.cottoneasy.portal.todo.consumer.view;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 待办缓存视图接口
 */
public interface BatchNumberUrlCacheableView extends ITodoView {

    /**
     * 获取创建待办或完成待办的字段值, 当前是批号
     */
    String getFieldValue();

    /**
     * 创建待办或完成待办操作列标题
     */
    String getOperateTitle();

    /**
     * 获取创建待办或完成待办 URL
     */
    String getUrlTemplate();

    /**
     * 获取创建待办或完成待办 URL 参数
     */
    Map<String, String> getParamsMap();

    /**
     * 获取创建待办或完成待办通知对象代码
     */
    String getTargetObjectCode();

    /**
     * 获取创建待办或完成待办通知对象名称
     */
    String getTargetObjectName();

    /**
     * 创建待办或完成待办的触发人 ID
     */
    String getCreateUserId();

    /**
     * 创建待办或完成待办的触发人名称
     */
    String getCreateUserName();

    /**
     * 创建待办或完成待办的触发时间
     */
    LocalDateTime getCreateTime();
}
