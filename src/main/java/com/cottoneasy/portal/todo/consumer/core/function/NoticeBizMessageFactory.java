package com.cottoneasy.portal.todo.consumer.core.function;

import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;

@FunctionalInterface
public interface NoticeBizMessageFactory{
    /**
     * 创建一个业务消息实体。
     *
     * @param view         标准化的待办视图
     * @param bizMessageId 生成的业务消息ID
     * @param bizConfigId  关联的业务配置ID
     * @return 创建好的实体
     */
    NoticeBizMessageEntity create(BatchNumberUrlArchiveCacheableView view, Long bizMessageId, Long bizConfigId);
}
