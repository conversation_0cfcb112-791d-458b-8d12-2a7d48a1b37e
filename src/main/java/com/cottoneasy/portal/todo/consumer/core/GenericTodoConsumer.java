package com.cottoneasy.portal.todo.consumer.core;

import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.TodoConstants;
import com.cottoneasy.portal.todo.cache.strategy.RefreshContext;
import com.cottoneasy.portal.todo.cache.strategy.TodoCacheRefreshStrategy;
import com.cottoneasy.portal.todo.consumer.view.ITodoView;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;

@Slf4j
@SuperBuilder
public class GenericTodoConsumer<V extends ITodoView> extends AbstractBaseTodoConsumer<V> {

    // --- 子类特有的依赖和行为 ---
    private final String consumerType;
    private final Function<Object, List<V>> dataTransformer;
    private final TodoCacheRefreshStrategy<V> cacheRefreshStrategy;
    private final TodoCacheService todoCacheService;
    private final TodoConstants.NOTICE_TYPE noticeType; // 某些策略需要

    @Override
    protected String getConsumerType() {
        return this.consumerType;
    }

    @Override
    protected List<V> transformToTodoModels(Object msg) {
        return this.dataTransformer.apply(msg);
    }

    @Override
    protected void refreshRedisOnSuccess(List<V> successfullyProcessedModels) {
        RefreshContext context = RefreshContext.builder()
                .consumerType(this.getConsumerType())
                .bizProcessType(this.bizProcessType)
                .noticeType(this.noticeType)
                .todoCacheService(this.todoCacheService)
                .build();
        this.cacheRefreshStrategy.refresh(successfullyProcessedModels, context);
    }
}
