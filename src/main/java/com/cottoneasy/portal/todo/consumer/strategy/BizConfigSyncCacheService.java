package com.cottoneasy.portal.todo.consumer.strategy;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.service.NoticeBizService;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.cache.ICache;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static com.cottoneasy.portal.todo.TodoConstants.REGION_BIZ_CONFIG;

/**
 *
 */
@Component
@Slf4j
public class BizConfigSyncCacheService {

    @Resource(name = REGION_BIZ_CONFIG)
    @Lazy
    private ICache cache;

    @Resource
    private NoticeBizService noticeBizService;

    @PostConstruct
    public void initBizConfig(){
        if(cache == null){
            log.error("待办业务配置缓存区域未初始化，请检查配置！！！！");
            return;
        }
        List<NoticeBizEntity> noticeBizEntityList = noticeBizService.getAllConfig();
        if(CollUtil.isEmpty(noticeBizEntityList)){
            log.error("");
            return;
        }
        for(NoticeBizEntity noticeBizEntity : noticeBizEntityList){
            String redisKey = noticeBizEntity.getNoticeBizCode();
            cache.put(redisKey,noticeBizEntity);
        }
    }
    /**
     *
     */
    public NoticeBizEntity getConfigByCode(String code){
        if(cache == null){
            log.error("待办业务配置缓存区域未初始化，请检查配置！！！！");
            return null;
        }
        Optional<Object> o = cache.get(code);
        if (o.isPresent()) {
            JSONObject jsonObject = (JSONObject) o.get();
            return jsonObject.toJavaObject(NoticeBizEntity.class);
        }
        return null;
    }
}
