package com.cottoneasy.portal.todo.consumer.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * redis 队列监听器
 *
 * <AUTHOR>
 * @date 2025/1/7
 */
@Slf4j
public class RedisQueueListener implements Runnable{

    private final RedisTemplate<String, Object> redisTemplate;
    private final String queue;
    private final RedisQueueConsumer redisQueueConsumer;
    private final Supplier<Boolean> runningStateSupplier;

    public RedisQueueListener(RedisTemplate<String, Object> redisTemplate,
                              String queue,
                              RedisQueueConsumer redisQueueConsumer,
                              Supplier<Boolean> runningStateSupplier) {
        this.redisTemplate = redisTemplate;
        this.queue = queue;
        this.redisQueueConsumer = redisQueueConsumer;
        this.runningStateSupplier = runningStateSupplier;
    }

    @Override
    public void run() {
        log.info("redis监听器开始监听:{}, 容器运行状态:{}", redisQueueConsumer.getQueueName(), runningStateSupplier.get());
        while (runningStateSupplier.get()) {
            try {
                Object msg = redisTemplate.opsForList().rightPop(queue, 30, TimeUnit.SECONDS);
                if (msg != null) {
                    try {
                        redisQueueConsumer.onMessage(msg);
                    } catch (Exception e) {
                        redisQueueConsumer.onError(msg, e);
                    }
                }
            } catch (QueryTimeoutException ignored) {
            } catch (Exception e) {
                if (runningStateSupplier.get()) {
                    log.error("Queue:{}", queue, e);
                } else {
                    log.info("QueueListener exits...queue:{}", queue);
                }
            }
        }
    }
}
