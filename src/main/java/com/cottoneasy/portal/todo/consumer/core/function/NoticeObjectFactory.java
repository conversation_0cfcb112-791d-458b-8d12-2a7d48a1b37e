package com.cottoneasy.portal.todo.consumer.core.function;

import com.cottoneasy.portal.base.entity.NoticeObjectEntity;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;

@FunctionalInterface
public interface NoticeObjectFactory {
    /**
     * 创建一个通知对象实体。
     *
     * @param view           标准化的待办视图
     * @param bizMessageId   关联的业务消息ID
     * @param noticeObjectId 生成的通知对象ID
     * @return 创建好的实体
     */
    NoticeObjectEntity create(BatchNumberUrlArchiveCacheableView view, Long bizMessageId, Long noticeObjectId);
}
