package com.cottoneasy.portal.todo.consumer.core;

import com.cottoneasy.portal.todo.TodoConstants;
import com.cottoneasy.portal.todo.consumer.core.function.ITodoProcessor;
import com.cottoneasy.portal.todo.consumer.view.ITodoView;
import lombok.RequiredArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.ILockService;
import org.harry.dandelion.framework.core.common.TransactionRoutingManager;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@SuperBuilder
public abstract class AbstractBaseTodoConsumer<V extends ITodoView> implements RedisQueueConsumer {

    @Value("${todo.profile:}")
    private String profile;

    private final ILockService lockService;

    private final String lock;

    private final TransactionRoutingManager transactionRoutingManager;

    protected final TodoConstants.BIZ_PROCESS_TYPE bizProcessType;

    private final TodoConstants.NOTICE_CHANNEL noticeChannel;

    private final ITodoProcessor<V> todoProcessor;

    @Override
    public String getQueueName() {
        return TodoConstants.buildChannelName(profile, bizProcessType, noticeChannel);
    }

    @Override
    public final void onMessage(Object msg) {
        // 1. 统一的数据转换
        List<V> todoModelList = transformToTodoModels(msg);
        if (Objects.isNull(todoModelList) || todoModelList.isEmpty()) {
            log.warn("监听到待办数据为空，不进行处理。消息: {}", msg);
            return;
        }

        Map<String, List<V>> todoByTargetObjectCode = todoModelList.stream()
                .collect(Collectors.groupingBy(ITodoView::getGroupingKey));

        for (Map.Entry<String, List<V>> entry : todoByTargetObjectCode.entrySet()) {
            String targetObjectCode = entry.getKey();
            List<V> todosForTarget = entry.getValue();

            String lockKey = String.format("%s:%s", lock, targetObjectCode);
            boolean locked = false;
            try {
                locked = lockService.lock(lockKey, 120 * 1000L, 1000L);
                if (!locked) {
                    log.info("客户 {} 待办处理锁获取失败，不进行处理。处理者: {}, 锁: {}", targetObjectCode,
                            this.getConsumerType(),
                            lockKey);
                    continue;
                }
                transactionRoutingManager.assertOpen();

                // 2. 预处理（钩子方法，子类可覆盖）
                List<V> processedList = preProcess(todosForTarget);
                if (Objects.isNull(processedList) || processedList.isEmpty()) {
                    log.warn("预处理后数据为空，跳过当前目标对象。目标对象: {}", targetObjectCode);
                    transactionRoutingManager.assertCommitAll();
                    continue;
                }

                // 3. 交给子类实现核心业务逻辑
                List<V> successfullyProcessedModels = todoProcessor.process(processedList);

                // 4. 成功后刷新缓存
                if (Objects.nonNull(successfullyProcessedModels) && !successfullyProcessedModels.isEmpty()) {
                    refreshRedisOnSuccess(successfullyProcessedModels);

                    // 5. 后处理（钩子方法，子类可覆盖）
                    postProcess(successfullyProcessedModels);
                } else {
                    log.info("客户: {} 未成功处理任何数据，不进行后处理。处理者: {}", targetObjectCode, getConsumerType());
                }

                transactionRoutingManager.assertCommitAll();
            } catch (Exception e) {
                transactionRoutingManager.assertRollbackAll();
                String bizIds = todosForTarget.stream().map(ITodoView::getBizId).collect(Collectors.joining(","));
                log.error("处理客户 {} 的待办消息时发生异常, 待办消息业务类型: {}, 待办消息业务ID: {}",
                        targetObjectCode,
                        todosForTarget.get(0).getNoticeType().toString(),
                        bizIds,
                        e);
            } finally {
                if (locked) {
                    lockService.unLock(lockKey);
                }
            }
        }
    }

    @Override
    public void onError(Object msg, Exception exception) {
        // 统一的错误处理
        log.warn("待办消息处理异常, 消息: {}, 处理者: {}", msg, getConsumerType(), exception);
    }

    /**
     * 获取消费者类型，用于日志记录
     *
     * @return 消费者类型描述
     */
    protected abstract String getConsumerType();

    /**
     * 将队列消息转换为领域模型列表。
     *
     * @param msg 原始消息
     * @return 模型列表
     */
    protected abstract List<V> transformToTodoModels(Object msg);

    /**
     * 成功处理后刷新 Redis 缓存。
     *
     * @param successfullyProcessedModels 已成功处理的模型列表
     */
    protected abstract void refreshRedisOnSuccess(List<V> successfullyProcessedModels);

    /**
     * 预处理数据, 可以进行去重、数据校验等操作 (钩子方法)。
     *
     * @param todoModelList 待办数据
     * @return 预处理后的数据
     */
    protected List<V> preProcess(List<V> todoModelList) {
        return todoModelList; // 默认不进行任何处理
    }

    /**
     * 后处理数据, 记录日志、清除资源等 (钩子方法)。
     *
     * @param ignored 已成功处理的模型列表
     */
    protected void postProcess(List<V> ignored) {
        // 默认不进行任何处理
    }
}
