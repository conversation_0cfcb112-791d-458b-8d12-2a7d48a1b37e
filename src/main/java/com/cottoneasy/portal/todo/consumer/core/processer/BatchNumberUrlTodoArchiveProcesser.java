package com.cottoneasy.portal.todo.consumer.core.processer;

import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.entity.NoticeBizMessageEntity;
import com.cottoneasy.portal.base.entity.NoticeContentEntity;
import com.cottoneasy.portal.base.entity.NoticeObjectEntity;
import com.cottoneasy.portal.todo.NoticeBizRepository;
import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.consumer.core.function.ITodoProcessor;
import com.cottoneasy.portal.todo.consumer.core.function.NoticeBizMessageFactory;
import com.cottoneasy.portal.todo.consumer.core.function.NoticeObjectFactory;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
@Builder
public class BatchNumberUrlTodoArchiveProcesser{

    private final BizIdGenerator bizIdGenerator;

    private final NoticeBizRepository noticeBizRepository;

    private final TodoCacheService todoCacheService;

    private final NoticeBizMessageFactory noticeBizMessageFactory;

    private final NoticeObjectFactory noticeObjectFactory;

    private final Function<BatchNumberUrlArchiveCacheableView, List<NoticeContentEntity>> modelToNoticeContentEntityList;

    public ITodoProcessor<BatchNumberUrlArchiveCacheableView> get() {
        return views -> {
            List<NoticeBizMessageEntity> noticeBizMessageEntities = new ArrayList<>();
            List<NoticeObjectEntity> noticeObjectEntities = new ArrayList<>();
            List<NoticeContentEntity> noticeContentEntities = new ArrayList<>();

            List<BatchNumberUrlArchiveCacheableView> successfullyProcessedModels = new ArrayList<>();
            for (BatchNumberUrlArchiveCacheableView todo : views) {
                Long bizMessageId = bizIdGenerator.generateBizId();
                Long bizConfigId = getConfigId(todo.getNoticeType().toString());

                NoticeBizMessageEntity noticeBizMessage = noticeBizMessageFactory.create(todo, bizMessageId, bizConfigId);

                Long noticeObjectId = bizIdGenerator.generateBizId();
                NoticeObjectEntity noticeObjectEntity = noticeObjectFactory.create(todo, bizMessageId, noticeObjectId);

                List<NoticeContentEntity> noticeContentEntityList = modelToNoticeContentEntityList.apply(todo);

                if (noticeContentEntityList.isEmpty()) {
                    log.warn("未找到待办内容数据，不进行处理, todo: {}", todo);
                    continue;
                }

                noticeContentEntityList.forEach(entity -> {
                    entity.setId(bizIdGenerator.generateBizId());
                    entity.setBizMessageId(bizMessageId);
                });

                noticeBizMessageEntities.add(noticeBizMessage);
                noticeObjectEntities.add(noticeObjectEntity);
                noticeContentEntities.addAll(noticeContentEntityList);
                successfullyProcessedModels.add(todo);
            }

            if (!noticeBizMessageEntities.isEmpty()) {
                noticeBizRepository.saveBatchNoticeBizMessage(noticeBizMessageEntities);
                noticeBizRepository.saveBatchNoticeObject(noticeObjectEntities);
                noticeBizRepository.saveBatchNoticeContent(noticeContentEntities);
            }
            return successfullyProcessedModels;
        };
    }

    private Long getConfigId(String todoType) {
        if (Objects.isNull(todoType) || todoType.isEmpty()) {
            return null;
        }
        NoticeBizEntity noticeBizEntity = todoCacheService.getNoticeBizConfig(todoType);
        return noticeBizEntity != null ? noticeBizEntity.getId() : null;
    }
}
