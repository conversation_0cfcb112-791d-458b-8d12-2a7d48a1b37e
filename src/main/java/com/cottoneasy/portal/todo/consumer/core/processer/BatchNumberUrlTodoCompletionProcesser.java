package com.cottoneasy.portal.todo.consumer.core.processer;

import cn.hutool.core.collection.CollUtil;
import com.cottoneasy.portal.todo.NoticeBizRepository;
import com.cottoneasy.portal.todo.consumer.core.function.ITodoProcessor;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlCacheableView;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public class BatchNumberUrlTodoCompletionProcesser {
    private final NoticeBizRepository noticeBizRepository;

    public ITodoProcessor<BatchNumberUrlCacheableView> get() {
        return views -> {
            if (CollUtil.isEmpty(views)) {
                return views;
            }

            // 因为列表中的 operateCode 都是相同的，所以可以直接获取
            String customCode = views.get(0).getTargetObjectCode();

            List<BatchNumberUrlCacheableView> noCreatorList = views.stream()
                    .filter(t -> Objects.isNull(t.getCreateUserId()))
                    .collect(Collectors.toList());
            List<String> bizIds = views.stream()
                    .map(BatchNumberUrlCacheableView::getBizId)
                    .collect(Collectors.toList());
            noticeBizRepository.updateTodoDisposeStatus(customCode, bizIds);

            views.removeAll(noCreatorList);

            if (!views.isEmpty()) {
                Map<String, List<BatchNumberUrlCacheableView>> byCreator = views.stream()
                        .collect(Collectors.groupingBy(BatchNumberUrlCacheableView::getCreateUserId));

                for (List<BatchNumberUrlCacheableView> todos : byCreator.values()) {
                    String createUserId = todos.get(0).getCreateUserId();
                    String createUserName = todos.get(0).getCreateUserName();
                    noticeBizRepository.updateTodoDisposeStatus(customCode, bizIds, createUserId, createUserName);
                }

            }
            return views;
        };
    }
}
