package com.cottoneasy.portal.todo.consumer.adapter;

import com.cottoneasy.portal.todo.TodoConstants;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlCacheableView;
import com.cottoneasy.portal.todo.model.impl.TraderPledgeConfirmCompletionModel;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

@RequiredArgsConstructor
public class TraderPledgeCompletionAdapter implements BatchNumberUrlCacheableView {

    private final TraderPledgeConfirmCompletionModel todo;

    @Override
    public String getGroupingKey() {
        return todo.getTargetObjectCode();
    }

    @Override
    public String getBizId() {
        return todo.getBizId();
    }

    @Override
    public String getFieldValue() {
        return todo.getBatchNumber();
    }

    @Override
    public String getOperateTitle() {
        return null;
    }

    @Override
    public String getUrlTemplate() {
        return todo.getHrefUrl();
    }

    @Override
    public Map<String, String> getParamsMap() {
        return todo.getParams();
    }

    @Override
    public TodoConstants.NOTICE_TYPE getNoticeType() {
        return todo.getNoticeType();
    }

    @Override
    public String getTargetObjectCode() {
        return todo.getTargetObjectCode();
    }

    @Override
    public String getTargetObjectName() {
        return todo.getCreateUserName();
    }

    @Override
    public String getCreateUserId() {
        return todo.getCreateUserId();
    }

    @Override
    public String getCreateUserName() {
        return todo.getCreateUserName();
    }

    @Override
    public LocalDateTime getCreateTime() {
        return todo.getCreateTime();
    }
}
