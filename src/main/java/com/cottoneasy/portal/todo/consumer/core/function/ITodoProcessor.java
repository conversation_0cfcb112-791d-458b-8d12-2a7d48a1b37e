package com.cottoneasy.portal.todo.consumer.core.function;

import com.cottoneasy.portal.todo.consumer.view.ITodoView;

import java.util.List;

/**
 * 通用的待办业务处理器接口。
 * <p>
 * 它定义了核心业务逻辑的契约，例如是将待办归档存库，还是更新已有待办的状态。
 *
 * @param <V> 处理器操作的视图类型，必须是 ITodoView 的子类型。
 */
@FunctionalInterface
public interface ITodoProcessor<V extends ITodoView> {

    /**
     * 执行核心业务处理。
     *
     * @param views 待处理的视图列表
     * @return 成功处理的视图列表
     */
    List<V> process(List<V> views);
}
