package com.cottoneasy.portal.todo.consumer.view;

import com.cottoneasy.portal.todo.TodoConstants;

/**
 * 基础待办视图接口。
 * <p>
 * 它定义了所有待办视图必须提供的最小数据契约，
 * 以便通用的消费者基类（AbstractBaseTodoConsumer）能够处理。
 */
public interface ITodoView {

    /**
     * 获取用于分组和加锁的业务键。
     */
    String getGroupingKey();

    /**
     * 获取业务ID，主要用于日志记录。
     */
    String getBizId();

    /**
     * 获取业务处理类型，主要用于日志记录。
     */
    TodoConstants.NOTICE_TYPE getNoticeType();
}
