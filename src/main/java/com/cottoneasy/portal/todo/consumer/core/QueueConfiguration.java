package com.cottoneasy.portal.todo.consumer.core;


/**
 * <AUTHOR>
 * @date 2025/1/7
 * @desc
 */
public class QueueConfiguration {

    /**
     * 队列名称
     */
    private String queue;
    /**
     * 消费者
     */
    private RedisQueueConsumer redisQueueConsumer;

    private QueueConfiguration() {
    }

    public static Builder builder() {
        return new Builder();
    }

    String getQueue() {
        return queue;
    }

    RedisQueueConsumer getConsumer() {
        return redisQueueConsumer;
    }

    public static class Builder {
        private final QueueConfiguration configuration = new QueueConfiguration();

        public QueueConfiguration defaultConfiguration(RedisQueueConsumer redisQueueConsumer) {
            configuration.redisQueueConsumer = redisQueueConsumer;
            configuration.queue = redisQueueConsumer.getClass().getSimpleName();
            return configuration;
        }

        public Builder queue(String queue) {
            configuration.queue = queue;
            return this;
        }

        public Builder consumer(RedisQueueConsumer redisQueueConsumer) {
            configuration.redisQueueConsumer = redisQueueConsumer;
            return this;
        }

        public QueueConfiguration build() {
            if (configuration.queue == null || configuration.queue.isEmpty()) {
                if (configuration.redisQueueConsumer != null) {
                    configuration.queue = configuration.getClass().getSimpleName();
                }
            }
            return configuration;
        }
    }
}
