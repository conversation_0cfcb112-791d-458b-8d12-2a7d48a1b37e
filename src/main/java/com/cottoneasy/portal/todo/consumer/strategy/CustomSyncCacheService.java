package com.cottoneasy.portal.todo.consumer.strategy;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cottoneasy.portal.base.entity.NoticeBizEntity;
import com.cottoneasy.portal.base.service.NoticeBizMessageService;
import com.cottoneasy.portal.base.vo.NoticeBizMessageVo;
import com.cottoneasy.portal.todo.TodoConstants;
import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.cache.ICache;
import org.harry.dandelion.framework.core.common.ILockService;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CustomSyncCacheService {

    @Resource(name = "Custom")
    @Lazy
    private ICache cache;

    @Resource
    private ILockService lockService;

    @Resource
    private BizConfigSyncCacheService bizConfigSyncCacheService;

    @Resource
    private NoticeBizMessageService noticeBizMessageService;


    /**
     * 依据业务操作，更新缓存信息
     * @param todoType
     */
    public void fillDataToRedis(TodoConstants.BIZ_PROCESS_TYPE todoType){
        if(cache == null){
            log.error("待办缓存未初始化，请检查配置！！！！");
            return;
        }
        NoticeBizEntity noticeBizEntity = bizConfigSyncCacheService.getConfigByCode(todoType.getBizConfigCode());
        List<NoticeBizMessageVo> noticeBizMessageVoList = noticeBizMessageService.getNoticeBizMessageVoList(noticeBizEntity.getId());
        Map<String, List<NoticeBizMessageVo>> noticeBizMessageMap = noticeBizMessageVoList.stream().collect(Collectors.groupingBy(o -> o.getCustomCode(), Collectors.toList()));

        Map<String, List<NoticeBizMessageViewVo>> noticeBizMessageViewVoMap = new HashMap<>();
        for(String key : noticeBizMessageMap.keySet()) {
            List<NoticeBizMessageVo> _noticeBizMsgList = noticeBizMessageMap.get(key);
            String customCode = _noticeBizMsgList.get(0).getCustomCode();
            List<NoticeBizMessageViewVo> _noticeBizMsgViewList = extractViweList(_noticeBizMsgList);
            noticeBizMessageViewVoMap.put(customCode, _noticeBizMsgViewList);
        }

        for(String customCode : noticeBizMessageViewVoMap.keySet()){
            String redisKey = StringUtil.joinWith(":", customCode, todoType);
            String lockName = StringUtil.joinWith("_", TodoConstants.TODO_REDIS_LOCK.CUSTOM_LOCK, customCode, todoType);
            boolean haslock = lockService.lock(lockName, 1000 * 60 * 30L, 500L);
            if (!haslock) {
                log.error("待办缓存空间获取锁失败！");
                return;
            }
            if (!cache.exists(redisKey)) {
                cache.evict(redisKey);
            }
            cache.put(redisKey, noticeBizMessageViewVoMap.get(customCode));
            lockService.unLock(lockName);
        }
    }

    /**
     * 从缓存中获取数据
     * @param todoType
     */
    public List<NoticeBizMessageViewVo> getRedisList(TodoConstants.BIZ_PROCESS_TYPE todoType, String customCode){
        List<NoticeBizMessageViewVo> existTodoViewList = new ArrayList<>();
        if(cache == null){
            log.error("待办缓存未初始化，请检查配置！！！！");
            return existTodoViewList;
        }

        String redisKey = StringUtil.joinWith(":", customCode, todoType);
        if(!cache.exists(redisKey)){
            log.info("客户待办缓存区不存在待办数据！");
            return existTodoViewList;
        }
        Optional<NoticeBizMessageViewVo> existTodoList = cache.get(redisKey);
        if(!existTodoList.isPresent()){
            return existTodoViewList;
        }
        Object obj = existTodoList.get();

        JSONArray jsonArray = JSONUtil.parseArray(obj);
        existTodoViewList.addAll(jsonArray.toList(NoticeBizMessageViewVo.class));
        return existTodoViewList;
    }

    /**
     * 从缓存中获取数据
     * @param todoType
     */
    public NoticeBizMessageViewVo getCurrentNoticeBizMessage(TodoConstants.BIZ_PROCESS_TYPE todoType,
                                                             TodoConstants.NOTICE_TYPE noticeType,
                                                             String customCode){
        if(cache == null){
            return null;
        }

        String redisKey = StringUtil.joinWith(":", customCode, todoType);
        if(!cache.exists(redisKey)) {
            log.info("客户待办缓存区不存在待办数据！");
            return null;
        }
        Optional<NoticeBizMessageViewVo> existTodoList = cache.get(redisKey);
        if(!existTodoList.isPresent()){
            return null;
        }
        Object obj = existTodoList.get();

        JSONObject jsonObject = JSONUtil.parseObj(obj);
        return jsonObject.toBean(NoticeBizMessageViewVo.class);
    }

    /**
     * 从缓存中获取数据
     * @param customCode
     * @param noticeBizMessageViewVoList
     */
    public void updRedisData(TodoConstants.BIZ_PROCESS_TYPE todoType, String customCode, List<NoticeBizMessageViewVo> noticeBizMessageViewVoList){
        if(cache == null){
            log.error("待办缓存未初始化，请检查配置！！！！");
            return;
        }

        String redisKey = StringUtil.joinWith(":", customCode, todoType);
        String lockName = StringUtil.joinWith("_", TodoConstants.TODO_REDIS_LOCK.CUSTOM_LOCK, customCode, todoType);

        boolean haslock = lockService.lock(lockName, 1000 * 60 * 30L, 500L);
        if (!haslock) {
            log.error("待办缓存空间获取锁失败！");
            return;
        }
        if(CollUtil.isNotEmpty(noticeBizMessageViewVoList)) {
            cache.put(redisKey, noticeBizMessageViewVoList);
        }else{
            cache.evict(redisKey);
        }
        lockService.unLock(lockName);
    }

    private List<NoticeBizMessageViewVo> extractViweList(List<NoticeBizMessageVo> _noticeBizMsgAllList){
        List<NoticeBizMessageViewVo> viewList = new ArrayList<>();
        Map<String,List<NoticeBizMessageVo>> _noticeBizMsgAllMap = _noticeBizMsgAllList.stream().collect(Collectors.groupingBy(NoticeBizMessageVo :: getBizType));
        for(String key : _noticeBizMsgAllMap.keySet()){
            List<NoticeBizMessageVo> _noticeBizMsgList = _noticeBizMsgAllMap.get(key);
            String customCode = _noticeBizMsgList.get(0).getCustomCode();
            String customName = _noticeBizMsgList.get(0).getCustomName();
            String bizType = _noticeBizMsgList.get(0).getBizType();
            String operateTitle = _noticeBizMsgList.get(0).getOperateTitle();
            String url = _noticeBizMsgList.get(0).getUrl();
            String batchNumbers = _noticeBizMsgList.stream().map(NoticeBizMessageVo :: getBatchNumbers).collect(Collectors.joining(","));
            Object[] argusmentArray = new Object[1];
            argusmentArray[0] = batchNumbers;
            url = MessageFormatter.arrayFormat(url, argusmentArray).getMessage();
            NoticeBizMessageViewVo noticeBizMessageViewVo = new NoticeBizMessageViewVo();
            noticeBizMessageViewVo.setCustomCode(customCode);
            noticeBizMessageViewVo.setCustomName(customName);
            noticeBizMessageViewVo.setBatchNumbers(batchNumbers);
            noticeBizMessageViewVo.setNoticeType(bizType);
            noticeBizMessageViewVo.setOperateTitle(operateTitle);
            noticeBizMessageViewVo.setUrl(url);
            viewList.add(noticeBizMessageViewVo);
        }
        return viewList;
    }
}
