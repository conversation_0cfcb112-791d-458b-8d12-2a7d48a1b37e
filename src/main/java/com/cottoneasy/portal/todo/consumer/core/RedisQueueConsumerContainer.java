package com.cottoneasy.portal.todo.consumer.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Redis 队列消费者容器, 用于管理多个 Redis 队列的消费者。
 * <p>会创建一个与消费者数量相等的线程池，每个消费者对应一个线程。如果没有消费者，容器将不会启动。</p>
 *
 * <AUTHOR>
 * @date 2025/1/7
 */
@Slf4j
public class RedisQueueConsumerContainer {
    private final Map<String, QueueConfiguration> consumerMap = new ConcurrentHashMap<>();
    private final RedisTemplate<String, Object> redisTemplate;
    // 提供给 Listener 的回调方法，用于检查容器是否仍在运行
    private volatile boolean running;
    // 线程池
    private ExecutorService exec;

    public RedisQueueConsumerContainer(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void addConsumer(QueueConfiguration configuration) {
        // 检查 configuration 对象本身是否为 null
        if (configuration == null) {
            log.warn("尝试添加一个空的 QueueConfiguration，已跳过。");
            return;
        }

        // 检查关键属性是否为 null
        if (configuration.getQueue() == null || configuration.getConsumer() == null) {
            log.warn("尝试添加一个不完整的 QueueConfiguration (队列或消费者为空)，已跳过。配置详情: {}", configuration);
            return;
        }

        // 检查队列名称是否已存在
        if (consumerMap.containsKey(configuration.getQueue())) {
            log.warn("队列名称:{} 已存在，旧的消费者将会被覆盖。", configuration.getQueue());
        }
        consumerMap.put(configuration.getQueue(), configuration);
    }

    public void destroy() {
        if (!running) {
            return;
        }
        log.info("正在关闭 redis 队列消费者容器...");
        running = false;
        if (this.exec != null) {
            this.exec.shutdown(); // 禁用新任务
            try {
                // 使用 awaitTermination 并设置合理的超时
                if (!this.exec.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("执行程序未在指定时间内终止.");
                    this.exec.shutdownNow(); // 尝试取消正在执行的任务
                    if (!this.exec.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.error("执行器未终止.");
                    }
                }
            } catch (InterruptedException ie) {
                log.error("关闭 redis 容器中断.", ie);
                this.exec.shutdownNow();
                // 保持中断状态
                Thread.currentThread().interrupt();
            }
        }
        log.info("Redis 队列消费者容器已关闭.");
    }

    public void init() {
        if (consumerMap.isEmpty()) {
            log.warn("未找到消费者，Redis 队列侦听器启动停止!");
            return;
        }
        if (running) {
            log.warn("容器已在运行.");
            return;
        }
        log.info("初始化 Redis 队列消费者容器, 共 {} 个消费者...", consumerMap.size());

        final String namePrefix = "RedisMQListener-";
        final AtomicInteger threadNumber = new AtomicInteger(1);
        ThreadFactory threadFactory = r -> {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            t.setDaemon(true);
            return t;
        };


        this.exec = Executors.newFixedThreadPool(consumerMap.size(), threadFactory);
        running = true;

        consumerMap.forEach((queue, config) -> {
            log.info("Submitting listener for queue: {}", queue);
            // 将 isRunning 状态传递给 Listener，让 Listener 自己决定何时停止
            exec.submit(new RedisQueueListener(redisTemplate, queue, config.getConsumer(), this::isRunning));
        });
    }

    private boolean isRunning() {
        return running;
    }
}
