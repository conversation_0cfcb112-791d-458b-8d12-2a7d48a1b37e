package com.cottoneasy.portal.todo.consumer.config;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.cottoneasy.portal.base.entity.NoticeContentEntity;
import com.cottoneasy.portal.todo.NoticeBizRepository;
import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.assembler.TodoModelAssembler;
import com.cottoneasy.portal.todo.cache.strategy.BatchNumberCacheRefreshStrategy;
import com.cottoneasy.portal.todo.cache.strategy.BatchNumberRemovalCacheRefreshStrategy;
import com.cottoneasy.portal.todo.consumer.adapter.*;
import com.cottoneasy.portal.todo.consumer.core.GenericTodoConsumer;
import com.cottoneasy.portal.todo.consumer.core.processer.BatchNumberUrlTodoArchiveProcesser;
import com.cottoneasy.portal.todo.consumer.core.processer.BatchNumberUrlTodoCompletionProcesser;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlCacheableView;
import com.cottoneasy.portal.todo.consumer.view.ITodoView;
import com.cottoneasy.portal.todo.model.impl.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.ILockService;
import org.harry.dandelion.framework.core.common.TransactionRoutingManager;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cottoneasy.portal.todo.TodoConstants.*;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class TodoConsumerConfig {

    private final ILockService lockService;
    private final TransactionRoutingManager transactionRoutingManager;
    private final BatchNumberCacheRefreshStrategy<BatchNumberUrlArchiveCacheableView> cacheRefreshStrategy;
    private final BatchNumberRemovalCacheRefreshStrategy cacheRemovalRefreshStrategy;
    private final TodoCacheService todoCacheService;
    private final NoticeBizRepository noticeBizRepository;

    @Bean
    public GenericTodoConsumer<BatchNumberUrlArchiveCacheableView> traderPledgeConfirmArchiveConsumer() {
        String errorMessage = "交易商质押待办消息归档数据转换异常";
        return this.archiveBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.TRADER_PLEDGE_CONFIRM)
                .noticeType(NOTICE_TYPE.TRADER_PLEDGE_CONFIRM)
                .lock(TODO_REDIS_LOCK.TRADER_PLEDGE_CONFIRM_LOCK.toString())
                .consumerType("交易商质押业务待办归档消费者")
                .dataTransformer(createDataTransformer(TraderPledgeConfirmModel.class, TraderPledgeConfirmAdaptor::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlArchiveCacheableView> warehousePledgeConfirmArchiveConsumer() {
        String errorMessage = "仓库质押待办消息归档数据转换异常";
        return this.archiveBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.WAREHOUSE_PLEDGE_CONFIRM)
                .noticeType(NOTICE_TYPE.WAREHOUSE_PLEDGE_CONFIRM)
                .lock(TODO_REDIS_LOCK.WAREHOUSE_PLEDGE_CONFIRM_LOCK.toString())
                .consumerType("仓库质押业务待办归档消费者")
                .dataTransformer(createDataTransformer(WarehousePledgeConfirmModel.class, WarehousePledgeConfirmAdaptor::new, errorMessage))
                .build();
    }


    @Bean
    public GenericTodoConsumer<BatchNumberUrlArchiveCacheableView> warehousePledgeSupplementaryArchiveConsumer() {
        String errorMessage = "仓库质押补章消息归档数据转换异常";
        return this.archiveBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.WAREHOUSE_PLEDGE_SUPPLEMENTARY)
                .noticeType(NOTICE_TYPE.WAREHOUSE_PLEDGE_SUPPLEMENTARY)
                .lock(TODO_REDIS_LOCK.WAREHOUSE_PLEDGE_SUPPLEMENTARY_LOCK.toString())
                .consumerType("仓库质押补章业务待办归档消费者")
                .dataTransformer(createDataTransformer(WarehousePledgeSupplementaryModel.class, WarehousePledgeSupplementaryAdaptor::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlArchiveCacheableView> sellerTransferConfirmArchiveConsumer() {
        String errorMessage = "卖方过户确认待办消息归档数据转换异常";
        return this.archiveBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.TRANSFER_CONFIRM)
                .noticeType(NOTICE_TYPE.SELLER_TRANSFER_OUT)
                .lock(TODO_REDIS_LOCK.SELLER_TRANSFER_CONFIRM_LOCK.toString())
                .consumerType("卖方过户确认业务待办归档消费者")
                .dataTransformer(createDataTransformer(SellerTransferConfirmModel.class, SellerTransferConfirmAdaptor::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlCacheableView> traderPledgeConfirmCompletionConsumer() {
        String errorMessage = "交易商质押待办消息完成数据转换异常";
        return this.completionBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.TRADER_PLEDGE_CONFIRM)
                .noticeType(NOTICE_TYPE.TRADER_PLEDGE_CONFIRM)
                .lock(TODO_REDIS_LOCK.TRADER_PLEDGE_CONFIRM_LOCK.toString())
                .consumerType("交易商质押业务待办完成消费者")
                .dataTransformer(createDataTransformer(TraderPledgeConfirmCompletionModel.class, TraderPledgeCompletionAdapter::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlCacheableView> warehousePledgeSupplementaryCompletionConsumer() {
        String errorMessage = "仓库质押待办消息完成数据转换异常";
        return this.completionBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.WAREHOUSE_PLEDGE_SUPPLEMENTARY)
                .noticeType(NOTICE_TYPE.WAREHOUSE_PLEDGE_SUPPLEMENTARY)
                .lock(TODO_REDIS_LOCK.WAREHOUSE_PLEDGE_SUPPLEMENTARY_LOCK.toString())
                .consumerType("仓库质押业务待办完成消费者")
                .dataTransformer(createDataTransformer(WarehousePledgeSupplementaryCompletionModel.class, WarehouseSupplementaryCompletionAdapter::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlCacheableView> warehousePledgeConfirmCompletionConsumer() {
        String errorMessage = "仓库质押待办消息完成数据转换异常";
        return this.completionBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.WAREHOUSE_PLEDGE_CONFIRM)
                .noticeType(NOTICE_TYPE.WAREHOUSE_PLEDGE_CONFIRM)
                .lock(TODO_REDIS_LOCK.WAREHOUSE_PLEDGE_CONFIRM_LOCK.toString())
                .consumerType("仓库质押业务待办完成消费者")
                .dataTransformer(createDataTransformer(WarehousePledgeConfirmCompletionModel.class, WarehousePledgeCompletionAdapter::new, errorMessage))
                .build();
    }

    @Bean
    public GenericTodoConsumer<BatchNumberUrlCacheableView> sellerTransferConfirmCompletionConsumer() {
        String errorMessage = "卖方过户确认待办消息完成数据转换异常";
        return this.completionBaseBuilder()
                .bizProcessType(BIZ_PROCESS_TYPE.TRANSFER_CONFIRM)
                .noticeType(NOTICE_TYPE.SELLER_TRANSFER_OUT)
                .lock(TODO_REDIS_LOCK.SELLER_TRANSFER_CONFIRM_LOCK.toString())
                .consumerType("卖方过户确认业务待办完成消费者")
                .dataTransformer(createDataTransformer(SellerTransferConfirmCompletionModel.class, SellerTransferCompletionAdapter::new, errorMessage))
                .build();
    }


    private GenericTodoConsumer.GenericTodoConsumerBuilder<BatchNumberUrlCacheableView, ?, ?> completionBaseBuilder() {
        // 批号 url 待办归档数据处理器
        BatchNumberUrlTodoCompletionProcesser processer = new BatchNumberUrlTodoCompletionProcesser(noticeBizRepository);

        return GenericTodoConsumer.<BatchNumberUrlCacheableView>builder()
                .noticeChannel(NOTICE_CHANNEL.TODO_COMPLETION)
                .todoProcessor(processer.get())
                .lockService(lockService)
                .transactionRoutingManager(transactionRoutingManager)
                .cacheRefreshStrategy(cacheRemovalRefreshStrategy)
                .todoCacheService(todoCacheService);
    }

    private GenericTodoConsumer.GenericTodoConsumerBuilder<BatchNumberUrlArchiveCacheableView, ?, ?> archiveBaseBuilder() {
        // 批号 url 待办归档数据处理器
        BatchNumberUrlTodoArchiveProcesser processer = BatchNumberUrlTodoArchiveProcesser.builder()
                .bizIdGenerator(BizIdGenerator.getInstance())
                .noticeBizRepository(noticeBizRepository)
                .todoCacheService(todoCacheService)
                .noticeBizMessageFactory(TodoModelAssembler::getNoticeBizMessageFromTodoModel)
                .noticeObjectFactory(TodoModelAssembler::getNoticeObjectFromTodoModel)
                .modelToNoticeContentEntityList(this::modelToNoticeContentEntityList)
                .build();

        return GenericTodoConsumer.<BatchNumberUrlArchiveCacheableView>builder()
                .noticeChannel(NOTICE_CHANNEL.TODO_ARCHIVE)
                .todoProcessor(processer.get())
                .lockService(lockService)
                .transactionRoutingManager(transactionRoutingManager)
                .cacheRefreshStrategy(cacheRefreshStrategy)
                .todoCacheService(todoCacheService);
    }

    /**
     * 一个通用的数据转换器，将消息体(JSON)转换为标准化的待办视图列表。
     *
     * @param modelClass     目标模型类，例如 TraderPledgeConfirmModel.class
     * @param adapterFactory 一个函数，用于将模型转换为视图适配器，例如 TraderPledgeConfirmAdaptor::new
     * @param errorMessage   转换失败时的错误信息
     * @param <T>            原始模型的类型
     * @param <V>            最终视图的类型，必须是 ITodoView 的子接口
     * @return 配置好的 Function 实例
     */
    private <T, V extends ITodoView> Function<Object, List<V>> createDataTransformer(
            Class<T> modelClass,
            Function<T, V> adapterFactory,
            String errorMessage) {
        return msg -> {
            try {
                JSONArray jsonArray = JSONUtil.parseArray(msg);
                List<T> models = jsonArray.toList(modelClass);
                return models.stream()
                        .map(adapterFactory)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("待办消息转换异常, 消息: {}", msg, e);
                // 将原始异常 e 作为 cause 传入，便于排查问题
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, errorMessage, e);
            }
        };
    }

    /**
     * 将待办视图转换为通知内容实体列表
     *
     * @param todo 待办视图
     * @return 通知内容实体列表
     */
    private List<NoticeContentEntity> modelToNoticeContentEntityList(BatchNumberUrlArchiveCacheableView todo) {
        return Arrays.asList(
                new NoticeContentEntity("batchNumber", todo.getFieldValue()),
                new NoticeContentEntity("bizType", todo.getNoticeType().getNoticeTypeName()),
                new NoticeContentEntity("operate", todo.getOperateTitle(), todo.getUrlTemplate())
        );
    }

}
