package com.cottoneasy.portal.todo.cache.strategy;

import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlCacheableView;
import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.cottoneasy.portal.todo.TodoConstants.*;

@Slf4j
@Component
public class BatchNumberRemovalCacheRefreshStrategy implements TodoCacheRefreshStrategy<BatchNumberUrlCacheableView> {

    @Override
    public void refresh(List<BatchNumberUrlCacheableView> data, RefreshContext context) {
        if (Objects.isNull(data) || data.isEmpty()) {
            log.info("处理者: {}, 未成功处理任何数据，不进行缓存刷新。", context.getConsumerType());
            return;
        }

        String consumerType = context.getConsumerType();
        BIZ_PROCESS_TYPE bizProcessType = context.getBizProcessType();
        NOTICE_TYPE noticeType = context.getNoticeType();
        TodoCacheService todoCacheService = context.getTodoCacheService();

        BatchNumberUrlCacheableView aModel = data.get(0);
        String targetObjectCode = aModel.getTargetObjectCode();
        NoticeBizMessageViewVo currentView = todoCacheService.get(targetObjectCode, bizProcessType, noticeType);

        if (Objects.isNull(currentView)) {
            log.info("处理者: {}, 未在缓存中找到待办视图，不进行刷新。客户: {}, 业务类型: {}",
                    consumerType, targetObjectCode, bizProcessType.toString());
            return;
        }

        // 去掉已经完成的批次号
        List<String> existingBatchNumbers = new ArrayList<>(Arrays.asList(currentView.getBatchNumbers().split(BATCH_NUMBER_SPLITTER)));

        List<String> completedBatchNumbers = data.stream()
                .map(BatchNumberUrlCacheableView::getFieldValue)
                .collect(Collectors.toList());

        boolean changed = existingBatchNumbers.removeAll(completedBatchNumbers);

        if (!changed) {
            log.warn("缓存刷新警告：在当前缓存的批次号中未找到任何需要完成的批次号。已完成批次: {}", completedBatchNumbers);
        }

        // 如果移除后列表为空，则整个缓存都应该被清除
        if (existingBatchNumbers.isEmpty()) {
            todoCacheService.put(targetObjectCode, bizProcessType, aModel.getNoticeType(), null);
            log.info("客户 {} 的所有待办均已完成，已清除缓存。", targetObjectCode);
        } else {
            // 否则，更新缓存
            String finalBatchNumbers = String.join(BATCH_NUMBER_SPLITTER, existingBatchNumbers);
            currentView.setBatchNumbers(finalBatchNumbers);

            String urlTemplate = aModel.getUrlTemplate();
            if (Objects.nonNull(urlTemplate)) {
                String updatedUrl = MessageFormatter.format(urlTemplate, finalBatchNumbers).getMessage();
                currentView.setUrl(updatedUrl);
            } else {
                log.warn("无法更新缓存中的URL，因为完成消息中缺少 hrefUrl 模板。");
            }

            // 处理已完成的参数
            Map<String, String> finalParams = handleUrlParams(currentView, data);
            currentView.setParamsMap(finalParams);

            todoCacheService.put(targetObjectCode, bizProcessType, aModel.getNoticeType(), currentView);
        }
    }

    /**
     * 清理已完成待办的 URL 参数。
     *
     * @param currentView     当前缓存视图
     * @param completionTodos 已完成的待办
     * @return 处理后的参数映射
     */
    public Map<String, String> handleUrlParams(NoticeBizMessageViewVo currentView, List<BatchNumberUrlCacheableView> completionTodos) { // 返回类型仍为String，但内部处理使用Set
        if (Objects.isNull(currentView) || Objects.isNull(currentView.getParamsMap())) {
            return null;
        }

        // 将当前视图的参数从 String 转换为 Set<String>，便于操作
        Map<String, Set<String>> currentParamsSet = new HashMap<>();
        currentView.getParamsMap().forEach((key, value) -> {
            currentParamsSet.put(key, new HashSet<>(Arrays.asList(value.split(PARAM_SPLITTER))));
        });

        // 收集所有需要移除的参数值
        // 注意：这里假设 BatchNumberUrlCacheableView::getFieldValue 返回的是需要移除的“批次号”或“字段值”
        // 并且这些值可能存在于 currentView 的某个参数的内部。
        // 如果 completionTodos 的 paramsMap 包含的是需要移除的键值对，则下面的逻辑需要调整。
        // 假设 completionTodos 的 paramsMap 提供了要移除的特定参数值。
        Map<String, Set<String>> paramsToRemove = new HashMap<>();
        for (BatchNumberUrlCacheableView todo : completionTodos) {
            todo.getParamsMap().forEach((key, value) -> {
                // 假设 value 可能是由 PARAM_SPLITTER 分隔的多个值，或者就是单个值
                paramsToRemove.computeIfAbsent(key, k -> new HashSet<>())
                              .addAll(Arrays.asList(value.split(PARAM_SPLITTER)));
            });
        }

        // 从 currentParamsSet 中移除已完成的参数
        paramsToRemove.forEach((key, valuesToRemove) -> {
            currentParamsSet.computeIfPresent(key, (k, existingValues) -> {
                existingValues.removeAll(valuesToRemove); // Set 的 removeAll 效率高
                return existingValues;
            });
        });

        // 将处理后的 Set<String> 转换回 Map<String, String>
        Map<String, String> finalParams = new HashMap<>();
        currentParamsSet.forEach((key, valueSet) -> {
            if (!valueSet.isEmpty()) {
                finalParams.put(key, String.join(PARAM_SPLITTER, valueSet));
            }
        });
        return finalParams;
    }
}
