package com.cottoneasy.portal.todo.cache.strategy;

import com.cottoneasy.portal.todo.TodoCacheService;
import com.cottoneasy.portal.todo.TodoConstants;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class RefreshContext {

    private final TodoCacheService todoCacheService;

    private final TodoConstants.BIZ_PROCESS_TYPE bizProcessType;

    private final TodoConstants.NOTICE_TYPE noticeType;

    private final String consumerType;

}
