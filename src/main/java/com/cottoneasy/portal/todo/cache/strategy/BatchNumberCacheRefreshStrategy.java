package com.cottoneasy.portal.todo.cache.strategy;

import cn.hutool.core.collection.CollUtil;
import com.cottoneasy.portal.todo.consumer.view.BatchNumberUrlArchiveCacheableView;
import com.cottoneasy.portal.todo.vo.NoticeBizMessageViewVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.cottoneasy.portal.todo.TodoConstants.BATCH_NUMBER_SPLITTER;
import static com.cottoneasy.portal.todo.TodoConstants.PARAM_SPLITTER;

/**
 * 基于批次号聚合的缓存刷新策略。
 * <p>
 * 封装了将多个待办按批次号合并，并更新缓存视图的通用逻辑。
 */
@Component
@Slf4j
public class BatchNumberCacheRefreshStrategy<T extends BatchNumberUrlArchiveCacheableView> implements TodoCacheRefreshStrategy<T> {

    @Override
    public void refresh(List<T> successfullyProcessedModels, RefreshContext context) {
        if (CollUtil.isEmpty(successfullyProcessedModels)) {
            log.info("处理者: {}, 未成功处理任何数据，不进行缓存刷新。", context.getConsumerType());
            return;
        }

        Map<String, List<BatchNumberUrlArchiveCacheableView>> bizListMap = successfullyProcessedModels.stream()
                .collect(Collectors.groupingBy(BatchNumberUrlArchiveCacheableView::getGroupingKey));

        for (String customCode : bizListMap.keySet()) {
            List<BatchNumberUrlArchiveCacheableView> todosForCustomCode = bizListMap.get(customCode);
            if (CollUtil.isEmpty(todosForCustomCode)) {
                continue;
            }

            // 获取当前通知对象的待办消息
            NoticeBizMessageViewVo currentView = context.getTodoCacheService().get(
                    customCode, context.getBizProcessType(), context.getNoticeType()
            );

            NoticeBizMessageViewVo finalView = (currentView == null)
                    ? createViewFromTodoList(todosForCustomCode)
                    : mergeViewWithTodoList(currentView, todosForCustomCode);

            Map<String, String> finalParams = handleUrlParams(currentView, todosForCustomCode);
            finalView.setBizProcessConfigId(context.getBizProcessType().getBizConfigCode());
            finalView.setNoticeType(context.getNoticeType().toString());
            finalView.setParamsMap(finalParams);

            context.getTodoCacheService().put(
                    customCode, context.getBizProcessType(), context.getNoticeType(), finalView
            );
        }
    }

    private NoticeBizMessageViewVo mergeViewWithTodoList(NoticeBizMessageViewVo currentView, List<BatchNumberUrlArchiveCacheableView> newTodos) {
        log.debug("当前缓存视图不为空, 合并待办消息");
        // 先对待办消息进行 hrefUrl, operatorTitle 分组
        Map<String, List<BatchNumberUrlArchiveCacheableView>> groupedTodos = newTodos.stream()
                .collect(Collectors.groupingBy(todo -> todo.getUrlTemplate() + "_" + todo.getOperateTitle()));

        if (groupedTodos.size() > 1) {
            log.warn("待办消息分组后，存在多个不同的 hrefUrl 或 operatorTitle，这不应该发生。请检查数据。");
            return currentView;
        }

        log.debug("当前缓存字段: {}", currentView.getBatchNumbers());
        // 获取所有待办消息的批次号
        Set<String> allBatchNumbers = newTodos.stream()
                .map(BatchNumberUrlArchiveCacheableView::getFieldValue)
                .collect(Collectors.toSet());

        // 如果存在当前待办消息, 则追加批次号
        if (!currentView.getBatchNumbers().isEmpty()) {
            allBatchNumbers.addAll(Arrays.asList(currentView.getBatchNumbers().split(BATCH_NUMBER_SPLITTER)));
        }

        // 拼接所有待办消息的批次号
        String finalBatchNumbers = String.join(BATCH_NUMBER_SPLITTER, allBatchNumbers);
        currentView.setBatchNumbers(finalBatchNumbers);

        log.debug("合并后的字段: {}", finalBatchNumbers);

        // 根据最新的批次号，重新格式化并更新 URL
        String urlTemplate = newTodos.get(0).getUrlTemplate();
        String updatedUrl = MessageFormatter.format(urlTemplate, finalBatchNumbers).getMessage();
        currentView.setUrl(updatedUrl);
        return currentView;
    }

    private NoticeBizMessageViewVo createViewFromTodoList(List<BatchNumberUrlArchiveCacheableView> newTodos) {
        log.debug("当前缓存视图为空, 创建新视图");
        NoticeBizMessageViewVo view = new NoticeBizMessageViewVo();
        BatchNumberUrlArchiveCacheableView aModel = newTodos.get(0);
        view.setCustomCode(aModel.getTargetObjectCode());
        view.setCustomName(aModel.getTargetObjectName());
        view.setNoticeType(aModel.getNoticeType().getNoticeTypeName());
        view.setOperateTitle(aModel.getOperateTitle());

        String allBatchNumbers = newTodos.stream()
                .map(BatchNumberUrlArchiveCacheableView::getFieldValue)
                .collect(Collectors.joining(BATCH_NUMBER_SPLITTER));
        view.setBatchNumbers(allBatchNumbers);
        return view;
    }

    /**
     * 合并当前视图和新待办列表的 URL 参数。
     *
     * @param currentView 当前缓存视图，可为 null。
     * @param newTodos    新的待办列表，可为 null 或空。
     * @return 一个包含所有参数并经过合并去重的新 Map。
     */
    public Map<String, String> handleUrlParams(NoticeBizMessageViewVo currentView, List<BatchNumberUrlArchiveCacheableView> newTodos) {
        // 1. 使用 Set 作为中间容器，在收集时直接去重。
        Map<String, Set<String>> paramsCollector = new HashMap<>();

        // 2. 用于将参数字符串安全地分解并添加到收集器中。
        BiConsumer<String, String> mergeFunction = (key, value) -> {
            if (value == null || value.isEmpty()) {
                return;
            }
            // computeIfAbsent: 如果 key 不存在，则创建新的 HashSet；否则，直接返回现有的 Set。
            Set<String> valueSet = paramsCollector.computeIfAbsent(key, k -> new HashSet<>());

            // 安全地分割、清理并添加元素
            String[] parts = value.split(PARAM_SPLITTER);
            for (String part : parts) {
                String trimmedPart = part.trim();
                if (!trimmedPart.isEmpty()) {
                    valueSet.add(trimmedPart);
                }
            }
        };

        // 3. 收集所有数据源的参数
        // a. 处理当前视图
        if (Objects.nonNull(currentView) && Objects.nonNull(currentView.getParamsMap())) {
            currentView.getParamsMap().forEach(mergeFunction);
        }

        // b. 处理新的待办列表
        if (CollUtil.isNotEmpty(newTodos)) {
            for (BatchNumberUrlArchiveCacheableView todo : newTodos) {
                if (Objects.nonNull(todo.getParamsMap())) {
                    for (Map.Entry<String, String> entry : todo.getParamsMap().entrySet()) {
                        String key = entry.getKey();
                        String value = entry.getValue();
                        mergeFunction.accept(key, value);
                    }
                }
            }
        }

        // 4. 将 Set<String> 拼接成最终的字符串。
        return paramsCollector.entrySet().stream()
                .collect(Collectors.toMap(
                        // 键保持不变
                        Map.Entry::getKey,
                        // 值拼接成字符串
                        entry -> String.join(PARAM_SPLITTER, entry.getValue())
                ));
    }
}
