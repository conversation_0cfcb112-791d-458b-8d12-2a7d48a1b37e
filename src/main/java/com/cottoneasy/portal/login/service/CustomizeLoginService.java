package com.cottoneasy.portal.login.service;

import com.cottoneasy.portal.login.utils.LoginStyleUtil;
import com.sinosoft.dandelion.system.client.model.Custom;
import com.sinosoft.dandelion.system.client.model.log.SystemAccessLogEsInfo;
import com.sinosoft.dandelion.system.client.service.CustomService;
import jodd.util.StringUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.SystemBootstrap;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service("login.exportCustomizeLogin.1")
@ApiRequestObject(value="导出自定义登录统计",name="CustomizeLoginService" , groups= {"登录统计"} ,params = {
        @ApiParamMeta(key = "searchVo", desc = "日志查询", type = SystemAccessLogEsInfo.class)
})
@ApiResponseObject(params= {
        @ApiParamMeta(key = "FilePath" , type = String.class, desc = "返回文件路径")
})
public class CustomizeLoginService  implements IBusinessService {

    @Autowired
    private ElasticsearchTemplate<SystemAccessLogEsInfo, String> systemLogEsInfoESTemplate;

    @Resource
    private CustomService customService;

    @Resource
    private SystemBootstrap bootstrap;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        FileOutputStream out = null;
        XSSFWorkbook wb = null;
        String path = null;
        try {
            SystemAccessLogEsInfo searchVo = context.getValueObject(SystemAccessLogEsInfo.class, "searchVo");
            Date startTime = searchVo.getStartTime();
            Date endTime = searchVo.getEndTime();

            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

            QueryBuilder loginLogqueryBuilder = QueryBuilders.termQuery("logType", "LoginLog");
            queryBuilder.must(loginLogqueryBuilder);

            RangeQueryBuilder timeStampRangeQueryBuilder = QueryBuilders.rangeQuery("startTime");

            timeStampRangeQueryBuilder.gte(DateUtils.getMillis(startTime));
            timeStampRangeQueryBuilder.lte(DateUtils.getMillis(endTime));
            queryBuilder.must(timeStampRangeQueryBuilder);

            List<SystemAccessLogEsInfo> removeList = new ArrayList<>();
            List<SystemAccessLogEsInfo> results = systemLogEsInfoESTemplate.searchMore(queryBuilder, 1000000, SystemAccessLogEsInfo.class);
            for (SystemAccessLogEsInfo systemLogEsInfo:results) {
                if (StringUtil.isEmpty(systemLogEsInfo.getCustomCode())){
                    removeList.add(systemLogEsInfo);
                }
            }
            results.removeAll(removeList);
            Map<List<String>, Long> map = results.stream().collect(Collectors.groupingBy(f -> Arrays.asList(f.getCustomCode(),
                    f.getCustomName(), f.getCustomType()), Collectors.counting()));
            List<List<String>> dataList = new ArrayList<>();
            for (List<String> key : map.keySet()) {
                String codeAndName = "";
                for (String value : key) {
                    if (value.equals("1")) {
                        codeAndName += "交易商";
                    } else if (value.equals("2")) {
                        codeAndName += "仓库";
                    } else if (value.equals("3")) {
                        codeAndName += "物流公司";
                    } else if (value.equals("4")) {
                        codeAndName += "加工单位";
                    } else if (value.equals("5")) {
                        codeAndName += "办事处";
                    } else if (value.equals("6")) {
                        codeAndName += "监管机构";
                    } else if (value.equals("7")) {
                        codeAndName += "保险公司";
                    } else if (value.equals("8")) {
                        codeAndName += "纤检机构";
                    } else if (value.equals("9")) {
                        codeAndName += "市场";
                    } else if (value.equals("10")) {
                        codeAndName += "市场交易商";
                    } else if (value.equals("11")) {
                        codeAndName += "普通企业";
                    } else if (value.equals("12")) {
                        codeAndName += "其他";
                    } else {
                        codeAndName += value;
                    }
                    codeAndName += "	";
                }
                // 添加隶属办事处
                Custom customByCode = customService.getCustomByCode(codeAndName.split("	")[0]);
                if (customByCode != null){
                    String attachmentName = customByCode.getAttachmentName();
                    codeAndName += StringUtil.isEmpty(attachmentName) ? "-" : attachmentName;
                    codeAndName += "	";
                    String dayLogin = codeAndName + map.get(key);
                    List<String> dayLogins = Arrays.asList(dayLogin.split("	"));
                    dataList.add(dayLogins);
                }
            }
            //标题
            String[] array = {"客户代码","客户名称","客户类型","隶属办事处","登录次数"};
            wb = new XSSFWorkbook();
            CellStyle titleStyle = LoginStyleUtil.createStyleForTitle(wb);
            XSSFSheet sheet = wb.createSheet("自定义登录统计");
            //标题
            Row row = sheet.createRow((int) 0);
            for (int i = 0; i < array.length; i++) {
                Cell cell = row.createCell(i);
                cell.setCellValue(array[i]);
                cell.setCellStyle(titleStyle);
            }
            //数据
            int index = 1;
            CellStyle cellStyle = LoginStyleUtil.createStyleForCell(wb);
            for (int i = 0; i < dataList.size(); i++) {
                XSSFRow row0 = sheet.createRow(index);
                index++;
                for (int k = 0; k < dataList.get(i).size(); k++) {
                    XSSFCell cell = row0.createCell(k);
                    if (k == 4){
                        int q = Integer.valueOf(dataList.get(i).get(k));
                        cell.setCellValue(q);
                    }else {
                        cell.setCellValue(dataList.get(i).get(k));
                    }
                    cell.setCellStyle(cellStyle);
                }
            }
            LoginStyleUtil.setSizeColumn(sheet,array.length);
            path = bootstrap.getExportPath() + File.separator + "自定义登录统计--" + new Date().getTime() + ".xlsx";
            out = new FileOutputStream(path);
            wb.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("文件导出失败：{}", e);
        }finally {
            try {
                wb.close();
                out.close();
            } catch (Exception e) {
                log.error("文件导出失败：{}", e);
            }

        }
        this.createSuccessResponse(context);
        context.getResponseBody().setData("FilePath", path);
    }
}
