package org.elasticsearch.client;

import org.apache.http.HttpResponse;
import org.apache.http.nio.protocol.HttpAsyncResponseConsumer;

public interface HttpAsyncResponseConsumerFactory {
    HttpAsyncResponseConsumerFactory DEFAULT = new HttpAsyncResponseConsumerFactory.HeapBufferedResponseConsumerFactory(1048576000);

    HttpAsyncResponseConsumer<HttpResponse> createHttpAsyncResponseConsumer();

    public static class HeapBufferedResponseConsumerFactory implements HttpAsyncResponseConsumerFactory {
        static final int DEFAULT_BUFFER_LIMIT = 1048576000;
        private final int bufferLimit;

        public HeapBufferedResponseConsumerFactory(int bufferLimitBytes) {
            this.bufferLimit = bufferLimitBytes;
        }

        public HttpAsyncResponseConsumer<HttpResponse> createHttpAsyncResponseConsumer() {
            return new HeapBufferedAsyncResponseConsumer(this.bufferLimit);
        }
    }
}
