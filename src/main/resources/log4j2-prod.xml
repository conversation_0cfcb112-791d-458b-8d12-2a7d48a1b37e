<?xml version="1.0" encoding="utf-8"?>

<configuration status="OFF">
	<Properties>
		 <property name="logLevel">DEBUG</property>
        <property name="logPath">${sys:catalina.home}/logs</property>
        <property name="logFileName">dandelion</property>
        <property name="logFileThresHoldSize">100M</property>
        <property name="logPattern">[%d{yyyy-MM-dd HH:mm:ss.SSS}] [日志ID:%X{logId}] [%-5level] [%c:%L]-%msg%n</property>
		<Property name="kafka_extends_fields">{"requestIp":"%X{requestIp}","requestId":"%X{requestId}","exceptionType":"%X{exceptionType}","logType":"%X{logType}","traceServerCodes":"%X{traceServerCodes}","traceServerIps":"%X{traceServerIps}","requestTime":"%X{requestTime}","receiveTime":"%X{receiveTime}","responseTime":"%X{responseTime}","consumTime":"%X{consumTime}","requestServiceId":"%X{requestServiceId}","requestServiceCode":"%X{requestServiceCode}","requestServiceName":"%X{requestServiceName}","requestServiceClassName":"%X{requestServiceClassName}","userId":"%X{userId}","loginName":"%X{loginName}","userName":"%X{userName}","userType":"%X{userType}","userIdentify":"%X{userIdentify}","customId":"%X{customId}","customName":"%X{customName}","customCode":"%X{customCode}","customType":"%X{customType}","customTypeName":"%X{customTypeName}","attachmentCustomCode":"%X{attachmentCustomCode}","attachmentCustomName":"%X{attachmentCustomName}","attachmentRegionCode":"%X{attachmentRegionCode}","attachmentRegionGroupCode":"%X{attachmentRegionGroupCode}","companyId":"%X{companyId}","companyCode":"%X{companyCode}","companyName":"%X{companyName}","companyType":"%X{companyType}","organId":"%X{organId}","organCode":"%X{organCode}","organName":"%X{organName}","organType":"%X{organType}"}</Property>
		<Property name="kafkaLogPattern">kafka||||%X{logId}||||%X{parentLogId}||||${sys:appCode}||||${sys:appName}||||${sys:serverCode}||||${sys:serverIp}||||%level||||%d{UNIX_MILLIS}||||${kafka_extends_fields}||||[%d{yyyy-MM-dd HH:mm:ss.SSS}]-[%thread]-[%c:%L] --- %message%n</Property>
	</Properties>
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT" >
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${logPattern}" />
		</Console>

		<Kafka name="primaryKafkaAppender"
		 	topic="topic_sdlogs"
			ignoreExceptions="false"
			syncSend="false"
			retryCount="3">
			<PatternLayout pattern="${kafkaLogPattern}" />
			<Property name="bootstrap.servers">*************:9091,*************:9091,*************:9091</Property>
			<Property name="max.block.ms">3000</Property>
			<Property name="timeout.ms">3000</Property>
			<Property name="buffer.memory">536870912</Property>
  			<Property name="batch.size">524288</Property>
  			<Property name="linger.ms">3000</Property>
  			<Property name="max.request.size">52428800</Property>
  			<Property name="acks">1</Property>
		</Kafka>


		<RollingFile name="kafkaFailoverKafkaLog"
			fileName="${logPath}/dandelion.log.kf"
			filePattern="${logPath}/dandelion-%d{yyyy-MM-dd}-%i.log.kf">
			<ThresholdFilter level="Debug" onMatch="ACCEPT" onMismatch="DENY" />
			<PatternLayout>
				<Pattern>${kafkaLogPattern}</Pattern>
			</PatternLayout>
			<Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${logFileThresHoldSize}"/>
			</Policies>

		</RollingFile>

		<Failover name="kafkaAppender" primary="primaryKafkaAppender" retryIntervalSeconds="600">
			<Failovers>
				<AppenderRef ref="kafkaFailoverKafkaLog" />
			</Failovers>
		</Failover>

		<RollingFile name="RootLogFileAppender"
                     fileName="${logPath}/${logFileName}.out"
                     filePattern="${logPath}/%d{yyyy-MM-dd}/${logFileName}-%d{yyyy-MM-dd}-%i.log.gz" append="true">
            <PatternLayout pattern="${logPattern}" />
            <Filters>
                <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${logFileThresHoldSize}"/>
            </Policies>
            <DefaultRolloverStrategy max="300" compressionLevel="8"/>
        </RollingFile>
	</Appenders>
	<Loggers>
		<!-- 项目日志基本控制 -->
		<AsyncLogger name="org.harry" level="${logLevel}" />
		<AsyncLogger name="com.sinosoft" level="${logLevel}" />
		<AsyncLogger name="com.cottoneasy" level="${logLevel}" />
		<AsyncLogger name="org.apache.kafka" level="ERROR" />
		<AsyncLogger name="org.elasticsearch" level="ERROR" />
		<!-- 整体日志级别控制 -->
		<asyncRoot level="warn" includeLocation="true">
<!--			<appender-ref ref="kafkaAppender" />-->
			<appender-ref ref="RootLogFileAppender" />
		</asyncRoot>
	</Loggers>
</configuration>
