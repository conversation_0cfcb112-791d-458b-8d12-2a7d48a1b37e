
██████   █████  ███    ██ ██████  ███████ ██      ██  ██████  ███    ██       ██████   ██████  ██████  ████████  █████  ██      
██   ██ ██   ██ ████   ██ ██   ██ ██      ██      ██ ██    ██ ████   ██       ██   ██ ██    ██ ██   ██    ██    ██   ██ ██      
██   ██ ███████ ██ ██  ██ ██   ██ █████   ██      ██ ██    ██ ██ ██  ██ █████ ██████  ██    ██ ██████     ██    ███████ ██      
██   ██ ██   ██ ██  ██ ██ ██   ██ ██      ██      ██ ██    ██ ██  ██ ██       ██      ██    ██ ██   ██    ██    ██   ██ ██      
██████  ██   ██ ██   ████ ██████  ███████ ███████ ██  ██████  ██   ████       ██       ██████  ██   ██    ██    ██   ██ ███████    
                                                                                                               
${AnsiColor.BRIGHT_GREEN}
Dandelion  Boot Version: 2.3.0
Spring Boot Version: ${spring-boot.version}${spring-boot.formatted-version}
${AnsiColor.BLACK}
