<?xml version="1.0" encoding="utf-8"?>
<!-- 开发环境日志配置 -->
<configuration status="warn">
    <Properties>
    	<!--日志级别-->
        <property name="logLevel">DEBUG</property>
		<!--控制台彩色日志输出格式-->
        <property name="consolePattern">%highlight{%d{yyyy-MM-dd HH:mm:ss.SSS}}{FATAL=red, ERROR=red, WARN=yellow, INFO=green, DEBUG=blue, TRACE=white} %highlight{[%-5level]}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=green, DEBUG=blue, TRACE=white} %style{[%c{1.}:%L]}{magenta} - %msg%n</property>
    </Properties>
	<Appenders>
		<!--控制台的配置（支持颜色）-->
		<Console name="Console" target="SYSTEM_OUT">
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${consolePattern}" disableAnsi="false" />
		</Console>
	</Appenders>

	<Loggers>
		<!-- 项目日志基本控制 -->
        <AsyncLogger name="org.harry" level="${logLevel}" />
        <AsyncLogger name="com.sinosoft" level="${logLevel}"/>
        <AsyncLogger name="com.cottoneasy" level="${logLevel}" />
        <!-- 整体日志级别控制 -->
        <AsyncRoot level="INFO"  includeLocation="true">
            <AppenderRef ref="Console"/>
        </AsyncRoot>
    </Loggers>
</configuration>
