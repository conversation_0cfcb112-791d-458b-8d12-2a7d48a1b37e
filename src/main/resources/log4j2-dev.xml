<?xml version="1.0" encoding="utf-8"?>
<!-- 开发环境日志配置 -->
<configuration status="warn">
    <Properties>
    	<!--日志级别-->
        <property name="logLevel">DEBUG</property>
		<!--日志输出格式-->
        <property name="logPattern">[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%-5level] [%c{1}:%L]-%msg%n</property>
    </Properties>
	<Appenders>
		<!--控制台的配置-->
		<Console name="Console" target="SYSTEM_OUT">
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${logPattern}" />
		</Console>
	</Appenders>
	
	<Loggers>
		<!-- 项目日志基本控制 -->
        <AsyncLogger name="org.harry" level="${logLevel}" />
        <AsyncLogger name="com.sinosoft" level="${logLevel}"/>
        <AsyncLogger name="com.cottoneasy" level="${logLevel}" />
        <!-- 整体日志级别控制 -->
        <AsyncRoot level="INFO"  includeLocation="true">
            <AppenderRef ref="Console"/>
        </AsyncRoot>
    </Loggers>
</configuration>