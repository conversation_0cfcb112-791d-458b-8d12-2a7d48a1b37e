<?xml version="1.0" encoding="utf-8"?>
<configuration status="warn">
    <Properties>
        <property name="logLevel">DEBUG</property>
        <property name="logPath">${sys:catalina.home}/logs</property>
        <property name="logFileName">dandelion</property>
        <property name="logFileThresHoldSize">50M</property>
        <property name="logPattern">[%d{yyyy-MM-dd HH:mm:ss.SSS}] [日志ID:%X{logId}] [%-5level] [%c:%L]-%msg%n</property>
    </Properties>
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT" >
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${logPattern}" />
		</Console>
        
        <RollingFile name="RootLogFileAppender"
                     fileName="${logPath}/${logFileName}.out"
                     filePattern="${logPath}/%d{yyyy-MM-dd}/${logFileName}-%d{yyyy-MM-dd}-%i.log" immediateFlush="false" append="true">
            <PatternLayout pattern="${logPattern}" />
            <Filters>
                <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${logFileThresHoldSize}"/>
            </Policies>
            <DefaultRolloverStrategy max="1000" compressionLevel="0"/>
        </RollingFile>
        
	</Appenders>
	<Loggers>
        <AsyncLogger name="org.harry" level="${logLevel}"/>
        <AsyncLogger name="com.sinosoft" level="${logLevel}"/>
        <AsyncLogger name="com.cottoneasy" level="${logLevel}" />
        <asyncRoot level="info" includeLocation="true">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RootLogFileAppender"/>
        </asyncRoot>
    </Loggers>
</configuration>