/*
 Navicat Premium Data Transfer

 Source Server         : 棉花用户测试
 Source Server Type    : MySQL
 Source Server Version : 50735
 Source Host           : ***********:3306
 Source Schema         : portal

 Target Server Type    : MySQL
 Target Server Version : 50735
 File Encoding         : 65001

 Date: 04/08/2022 09:37:18
*/

SET NAMES utf8;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for de_cms_complain
-- ----------------------------
DROP TABLE IF EXISTS `de_cms_complain`;
CREATE TABLE `de_cms_complain`
(
    COMPLAIN_ID    varchar(50)  NOT NULL COMMENT '投诉ID',
    CUSTOM_NAME    varchar(25)  COMMENT '客户名称',
    CUSTOM_ID      varchar(50)  COMMENT '客户id',
    ANONYMOUS      varchar(20)  COMMENT '是否匿名',
    TELPHONE       varchar(64)  COMMENT '联系电话',
    CONTENT       varchar(512)  COMMENT '投诉内容',
    SUBMIT_TIME    datetime  COMMENT '提交时间',
    STATUS         varchar(20)  COMMENT '处理状态',
    DEAL_RESULT    varchar(255)  COMMENT '处理结果',
    DEAL_TIME      datetime  COMMENT '处理时间',
    DEAL_USER_ID   varchar(50)  COMMENT '处理人id',
    DEAL_USER_NAME varchar(25)  COMMENT '处理人名字',
    PRIMARY KEY (COMPLAIN_ID)
);
alter table de_cms_complain COMMENT = '投诉信息表';


-- ----------------------------
-- Table structure for de_cms_purchase
-- ----------------------------
DROP TABLE IF EXISTS `de_cms_purchase`;
CREATE TABLE `de_cms_purchase`
(
    ID             varchar(32)  NOT NULL,
    REMARK         varchar(1000)  ,
    CARD_COTTON    int(11)  COMMENT '是否GC/绿卡棉',
    CONTACT        varchar(32)  COMMENT '联系人',
    CONTACT_NUM    varchar(100)  COMMENT '联系电话/QQ/微信',
    COMPANY_NAME   varchar(128)  COMMENT '企业名称',
    CREATE_TIME    datetime  COMMENT '创建时间',
    CREATE_USER_ID varchar(32)  COMMENT '创建人ID',
    CREATE_USER    varchar(128)  COMMENT '创建人',
    UPDATE_TIME    datetime  COMMENT '最近更新时间',
    UPDATE_USER_ID varchar(32)  COMMENT '更新人ID',
    UPDATE_USER    varchar(128)  COMMENT '更新人',
    PRIMARY KEY (ID)
);



drop table if exists T_PORTAL_CUSTOM_DATA_CONFIRM_FORM;

/*==============================================================*/
/* Table: T_PORTAL_CUSTOM_DATA_CONFIRM_FORM                     */
/*==============================================================*/
create table T_PORTAL_CUSTOM_DATA_CONFIRM_FORM
(
   ID                   bigint not null auto_increment  comment 'ID',
   CUSTOME_CODE         varchar(32)  comment 'CUSTOME_CODE',
   CUSTOME_NAME         varchar(512)  comment 'CUSTOME_NAME',
   OFFICE_CODE          varchar(32)  comment '办事处代码',
   OFFICE_NAME          varchar(32)  comment '办事处名称',
   CONFIRMED            integer  comment '是否已确认',
   CONFIRM_DATE         datetime  comment '确认时间',
   CONFIRM_USER_NAME    varchar(256)  comment '确认人',
   CONFIRM_USER_ID      varchar(64)  comment '确认人ID',
   CHECKER      varchar(64)  comment '核对人',
   CONTACT_NUM      varchar(100)  comment '联系电话',
   GROUP_ACCOUNT_BALANCE varchar(32)  comment '集团账户余额',
   GROUP_ACCOUNT_LOAN_BALANCE varchar(32)  comment '集团账户贷款金额',
   GROUP_ACCOUNT_CONFIRM_RESULT integer  comment '集团账户确认结果',
   GROUP_ACCOUNT_CONFIRM_DESC varchar(1000)  comment '集团账户确认详情',
   ECOMMERCE_ACCOUNT_BALANCE varchar(32)  comment '电商账户余额',
   ECOMMERCE_ACCOUNT_LOAN_BALANCE varchar(32)  comment '电商账户贷款金额',
   ECOMMERCE_ACCOUNT_CONFIRM_RESULT integer  comment '电商账户确认结果',
   ECOMMERCE_ACCOUNT_CONFIRM_DESC varchar(1000)  comment '电商账户确认详情',
   LOGISTICS_ACCOUNT_BALANCE varchar(32)  comment '配送账户余额',
   LOGISTICS_ACCOUNT_LOAN_BALANCE varchar(32)  comment '配送账户贷款金额',
   LOGISTICS_ACCOUNT_CONFIRM_RESULT integer  comment '配送账户确认结果',
   LOGISTICS_ACCOUNT_CONFIRM_DESC varchar(1000)  comment '配送账户确认详情',
   INTERNAL_TRADE_ACCOUNT_BALANCE varchar(32)  comment '国贸账户余额',
   INTERNAL_TRADE_ACCOUNT_LOAN_BALANCE varchar(32)  comment '国贸账户贷款金额',
   INTERNAL_TRADE_ACCOUNT_CONFIRM_RESULT integer  comment '国贸账户确认结果',
   INTERNAL_TRADE_ACCOUNT_CONFIRM_DESC varchar(1000)  comment '国贸账户确认详情',
   TRADE_BATCH_NUM      varchar(32)  comment '交易批数',
   TRADE_WEIGHT         varchar(32)  comment '交易重量',
   TRADE_CONFIRM_RESULT integer  comment '交易确认结果',
   TRADE_CONFIRM_DESC   varchar(1000)  comment '交易确认详情',
   FINANCE_BATCH_NUM    varchar(32)  comment '融资批数',
   FINANCE_WEIGHT       varchar(32)  comment '融资重量',
   FINANCE_CONFIRM_RESULT integer  comment '融资确认结果',
   FINANCE_CONFIRM_DESC varchar(1000)  comment '融资确认详情',
   SUPERVISION_BATCH_NUM varchar(32)  comment '监管批数',
   SUPERVISION_WEIGHT   varchar(32)  comment '监管重量',
   SUPERVISION_CONFIRM_RESULT integer  comment '监管确认结果',
   SUPERVISION_CONFIRM_DESC varchar(1000)  comment '监管确认详情',
   CREATE_USER_NAME     varchar(256)  comment '创建人',
   CRTATE_USER_ID       varchar(32)  comment '创建人ID',
   CREATE_TIME          datetime  comment '创建时间',
   UPDATE_USER_NAME     varchar(256)  comment '更新人',
   UPDATE_USER_ID       varbinary(32)  comment '更新人ID',
   UPDATE_TIME          datetime  comment '更新时间',
   primary key (ID)
);

alter table T_PORTAL_CUSTOM_DATA_CONFIRM_FORM comment '统一确认结果：0 ：未确认   1： 已确认      -1：有异议';



/*==============================================================*/
/* Table: DE_USER_LOGIN_AGREEMENT                     */
/*==============================================================*/
drop table if exists DE_USER_LOGIN_AGREEMENT;
create table DE_USER_LOGIN_AGREEMENT
(
   ID                  integer not null auto_increment  comment 'ID',
   NAME        				  varchar(512)  comment '协议名称',
   TITLE         				varchar(512)  comment '标题',
   CONTENT          		text  comment '内容',
   SOURCE          			varchar(32)  comment '来源',
   AUTHOR               varchar(32)  comment '作者',
   DEFAULT_AGREEMENT    integer  comment '默认协议 0:否   1:是 ',
   LIMIT_CUSTOM_TYPE    varchar(32)  comment '限制登录客户类型',
   CREATE_DATE      		datetime  comment '创建协议时间',
   primary key (ID)
);

alter table DE_USER_LOGIN_AGREEMENT comment '用户登录协议';




/*==============================================================*/
/* Table: DE_USER_LOGIN_AGREE_RECORD                     */
/*==============================================================*/
drop table if exists DE_USER_LOGIN_AGREE_RECORD;
create table DE_USER_LOGIN_AGREE_RECORD
(
   ID                   integer not null auto_increment  comment 'ID',
   AGREEMENT_ID         integer  comment '协议ID',
   ENTERPRISE_ID        varchar(32)  comment '企业ID',
   CUSTOM_ID          	varchar(32)  comment '客户ID',
   USER_ID          		varchar(32)  comment '用户ID',
   LOGIN_NAME           varchar(32)  comment '登录账号',
   CUSTOM_CODE    			varchar(32)  comment '客户代码',
   CUSTOM_TYPE          varchar(32)  comment '客户类型',
   CREATE_TIME    			datetime  comment '创建时间',
   UPDATE_TIME      		datetime  comment '更新时间',
   primary key (ID)
);

alter table DE_USER_LOGIN_AGREE_RECORD comment '用户登录同意协议记录';

