#业务系统相关配置
system:
  code: 1009
  name: dandelion-cms
  workid: 2
  enableGlobalCache: true
  filePath: /portal/
  druid-stat: #druid监控配置
    enable: true
    loginUsername: admin
    loginPassword: zkr!<EMAIL>
  login:
    enableCaptcha: false
    hashAlgorithmName: SHA-256 #密码加密策略 md5 SHA-256
    hashIterations: 1 #密码哈希次数 默认3hashAlgorithmName: SHA-256 #密码加密策略 md5 SHA-256
    cahceManager: redis
    indexUrl: /portal/redirect?token={token}
    filterPath:
      - url: /websocket/**
        permission: anon
      - url: /rest/componentService/**
        permission: anon
      - url: /rest/cmsCatalog/ListCatalogAndComTree
        permission: anon
      - url: /rest/cmsCatalog/ListCmsCatalogAndCom
        permission: anon
      - url: /rest/cmsCatalog/PageCmsCatalogBySite
        permission: anon
      - url: /rest/cmsCatalog/TreeCmsCatalogBySite
        permission: anon
      - url: /rest/cms/index
        permission: anon
      - url: /rest/complain/addComplain
        permission: anon
      - url: /rest/region/getregiongroups
        permission: anon
      - url: /rest/dict/getdictlist
        permission: anon
      - url: /rest/region/getregions
        permission: anon
      - url: /webUpload
        permission: anon
      - url: /rest/bank/queryAccountBanks
        permission: anon
      - url: /rest/purchase/addPurchase
        permission: anon
      - url: /rest/cmsContent/PageCmsContentAndTemService
        permission: anon
      - url: /rest/es/querySitSearchIndex
        permission: anon
      - url: /rest/loginagreement/getloginagreement
        permission: anon
      - url: /rest/loginagreement/requirecheckedagreement
        permission: anon
      - url: /cmsResource/**
        permission: anon
#web容器配置
server:
  port: 8080
  servlet:
    context-path: /portal

#spring配置(文件上传大小、flyway、aop、数据源)
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 300MB
  aop:
    proxy-target-class: true
  main:
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  resource:
    static-locations: classpath:/static/,classpath:/public/
  flyway:
    enabled: false
    locations: classpath:scripts/mysql
    baseline-on-migrate: true
    baseline-version: 0
  redis:
      host: ***********
      port: 7010
      password: cnce@321
      timeout: 3000
      pool:
         #最大连接数(负数表示没有限制)
         max-active: 100
         #最大空闲连接
         max-idle: 10
         #最大阻塞等待时间(负数表示没有限制)
         max-wait: 100000
  datasource:
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 100
        max-wait: 60000
        validation-query: SELECT 1 FROM DUAL
        filters: stat,slf4j
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 3000000
        test-while-idle: true
        test-on-borrow: true
        test-on-return: false
        query-timeout: 0
        transaction-query-timeout: 0
        connect-timeout: 60000
        socket-timeout: 600000
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      datasource:
        master:
          url: **************************************************************************************************************************************************************************
          username: cnce
          password: Kylin@123
          driver-class-name: com.mysql.cj.jdbc.Driver
        uc:
          url: **********************************************************************************************************************************************************************
          username: cnce
          password: Kylin@123
        cfcara:
          url: ***************************************
          username: cfcaratest
          password: Cnce@2024
          driver-class-name: oracle.jdbc.driver.OracleDriver
  besmq:
    brokerURL: tcp://***********:3200
    username: admin
    password: admin
    #以下监听用户中心主题，目前监听用户中心缓存、账号、字典、区域等数据变化，用户中心应用无需配置
    subscribeTopicNames: TOPIC_2000
    enableSystemTopicListener: true
#日志配置
logging:
  config: classpath:log4j2-pre.xml
#mybatis配置
mybatis-plus:
  mapper-locations: classpath*:org/harry/**/mappings/*.xml,classpath*:com/sinosoft/**/mappings/**/*.xml,classpath*:com/cottoneasy/**/mappings/**/*.xml # mapper映射文件位置
  typeHandlersPackage: com.sinosoft.dandelion.system.client.typehandler
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
elasticsearch:
   host: ***********:9200
   #连接池里的最大连接数
   max_connect_total: 30
   #某一个/每服务每次能并行接收的请求数量
   max_connect_per_route: 10
   #http clilent中从connetcion pool中获得一个connection的超时时间
   connection_request_timeout_millis: 2000
   #响应超时时间，超过此时间不再读取响应
   socket_timeout_millis: 30000
   #链接建立的超时时间
   connect_timeout_millis: 2000
   username: elastic
   password: cnce@2024
#swagger配置
swagger:
  enable: false
  basePackage: org.harry;com.sinosoft;com.cottoneasy
  title: cncotton-trade-service-system 接口文档
  group: cncotton-trade-service-system 接口说明文档
  description: cncotton-trade-service-system API 接口扫描、执行、测试工具
  version: 1.0
  author: harry
  url: http://www.sinodandelion.com
  email: <EMAIL>
  license: ©2019-2022 Dandelion Group. All rights reserved.
  licenseUrl:
#文件SDK配置
forest:
  bean-id: forestConfiguration # 在spring上下文中bean的id, 默认值为forestConfiguration
  backend: okhttp3 # 后端HTTP API： okhttp3
  max-connections: 1000 # 连接池最大连接数，默认值为500
  max-route-connections: 500 # 每个路由的最大连接数，默认值为500
  timeout: 60000 # 请求超时时间，单位为毫秒, 默认值为3000
  connect-timeout: 60000 # 连接超时时间，单位为毫秒, 默认值为2000
  retry-count: 0 # 请求失败后重试次数，默认为0次不重试
  logEnabled: true # 打开或关闭日志，默认为true
  log-request: true # 打开/关闭Forest请求日志（默认为 true）
  log-response-status: true # 打开/关闭Forest响应状态日志（默认为 true）
  log-response-content: true # 打开/关闭Forest响应内容日志（默认为 false）
  variables:
    fms: #配置文件服务中心访问授权码和分配的资源码
      serverUrl: http://***********/dandelion-fms-server
      accessSecretKey: d2gxNjI0MjQ2NjA2ODEx
      repCode: 1004
#签章服务配置
signature:
  enable: true
  protocol: http
  #host: ***************
  host: ***********
  port: 8184
  #hash加密方式
  hashalg: hash256
  #图片签章操作员
  operatorCode: 900992
  #文本签章操作员
  manageOptCode: 1003
  manageSealCode: yz20230102427652596
  managePassword: 111111
ra:
  enable: true
