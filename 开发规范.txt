1、包名尽量简短，能说明清楚就行
2、service中，使用entity进行参数接收和响应，不满足时再创建VO类。
3、单表操作时全部使用mybaits-plus提供的单表操作方法
4、服务名一定要以这几个单词开头{"create","add","increase","bind","sync","delete","del","update","upd","edit"}
5、对于create_user_id,create_user_name，这样的通用字段，要建立baseEntity，不要在每个实体中，都去写这些字段，可以参考dandelion-system的代码
6、建立字典枚举类，不要拿1、2、3这样的数据更新数据库，要通过调用枚举类，更新状态，例如 Entity.setStatus(Constants.status),这种更新业务状态
7、service注释格式
	/**
	 * 
	 * @Title: GetEntrustListService.java
	 * @Description: 交易商品-获取商品最新10条委托
	 * <AUTHOR>
	 * @date 2021年10月22日
	 * @version V1.0
	 */
8、h2数据库、mysql数据库的SQL脚本注意点：(1)h2不支持ALERT语句(2)mysql构造创建语句中的"CHARACTER SET utf8 COLLATE utf8_general_ci"等设置
